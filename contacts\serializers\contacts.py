from rest_framework import serializers
from contacts.models.contacts import Contact, ContactHistory, ContactType
from users.serializers.users import UserDetailSerializer
from django.utils.encoding import force_str

class ContactTypeSerializer(serializers.ModelSerializer):
    # Ensure text fields properly handle Unicode
    name = serializers.Char<PERSON>ield(max_length=255)
    
    class Meta:
        model = ContactType
        fields = ['id', 'name', 'created_at']
        read_only_fields = ['id', 'created_at']

class ContactCreateSerializer(serializers.ModelSerializer):
    type_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        write_only=True
    )
    
    # Ensure text fields properly handle Unicode
    name = serializers.CharField(max_length=255, required=True)
    email = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    phone = serializers.CharField(max_length=20, required=False, allow_null=True, allow_blank=True)
    company = serializers.Char<PERSON>ield(max_length=255, required=False, allow_null=True, allow_blank=True)
    address = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    
    class Meta:
        model = Contact
        fields = ['name', 'email', 'phone', 'company', 'address', 'type_ids', 'is_deleted']
        extra_kwargs = {
            'name': {'required': True}
        }
    
    def validate(self, data):
        # Validate type_ids exist if provided
        type_ids = data.pop('type_ids', [])
        if type_ids:
            for type_id in type_ids:
                try:
                    ContactType.objects.get(pk=type_id)
                except ContactType.DoesNotExist:
                    raise serializers.ValidationError({"type_ids": f"Contact type with ID {type_id} does not exist"})
                    
        # Store the validated IDs for use in create/update
        self.validated_type_ids = type_ids
        
        return data
    
    def create(self, validated_data):
        contact = Contact.objects.create(**validated_data)
        
        # Add types
        if hasattr(self, 'validated_type_ids') and self.validated_type_ids:
            types = ContactType.objects.filter(id__in=self.validated_type_ids)
            contact.types.set(types)
            
        return contact
        
    def update(self, instance, validated_data):
        # Update types if provided
        if hasattr(self, 'validated_type_ids'):
            types = ContactType.objects.filter(id__in=self.validated_type_ids)
            instance.types.set(types)
        
        # Update other fields
        for field, value in validated_data.items():
            setattr(instance, field, value)
            
        instance.save()
        return instance

    # Add this method to build a contact without saving it
    def build_contact(self, **extra_fields):
        """
        Build a Contact instance from validated data without saving
        Useful for optimizing the creation process
        """
        validated_data = dict(self.validated_data)
        
        # Remove M2M fields that need to be handled separately
        if 'type' in validated_data:
            validated_data.pop('type')
            
        # Create instance without saving to database yet
        contact = Contact(**validated_data, **extra_fields)
        return contact

class ContactSimpleSerializer(serializers.ModelSerializer):
    type_names = serializers.SerializerMethodField()
    
    class Meta:
        model = Contact
        fields = ['id', 'name', 'email', 'phone', 'company', 'type_names']
        
    def get_type_names(self, obj):
        return [type_obj.name for type_obj in obj.types.all()]

class ContactHistorySerializer(serializers.ModelSerializer):
    modified_by = UserDetailSerializer(read_only=True)
    
    class Meta:
        model = ContactHistory
        fields = ['id', 'modified_at', 'modified_by', 'data']
        read_only_fields = fields

class ContactDetailSerializer(serializers.ModelSerializer):
    created_by = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()
    sharedLocations = serializers.SerializerMethodField()
    ownedLocations = serializers.SerializerMethodField()
    balance = serializers.SerializerMethodField()

    class Meta:
        model = Contact
        fields = [
            'id', 'name', 'email', 'phone', 'company', 'type', 'is_deleted',
            'balance', 'sharedLocations', 'ownedLocations', 'created_at', 'created_by'
        ]
        read_only_fields = fields

    def get_created_by(self, obj):
        return obj.created_by.email if obj.created_by else None

    def get_type(self, obj):
        return obj.type_list

    def get_sharedLocations(self, obj):
        shared_locations = obj.get_shared_locations()
        if shared_locations:
            shared_locations = shared_locations.values('id', 'name', 'address', 'capacity', 'is_active')
        return list(shared_locations) if shared_locations else []

    def get_ownedLocations(self, obj):
        owned_locations = obj.get_owned_locations()
        if owned_locations:
            owned_locations = owned_locations.values('id', 'name', 'address', 'capacity', 'is_active')
        return list(owned_locations) if owned_locations else []

    def get_balance(self, obj):
        return obj.calculate_balance()