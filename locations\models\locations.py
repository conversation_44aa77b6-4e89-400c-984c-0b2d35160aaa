import uuid
from django.db import models
from django.utils import timezone
from contacts.models.contacts import Contact  # Ensure this import is correct and the Contact model exists
from users.models.users import User
from contacts.models.contacts import OurCompany


class LocationType(models.Model):
    """Model for location types"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_location_types'
    )
    
    def __str__(self):
        return self.name
    
    class Meta:
        ordering = ['name']

class Location(models.Model):
    """Model for advertising locations like billboards, screens, etc."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    address = models.TextField()
    
    # Replace the single type with a many-to-many relationship
    types = models.ManyToManyField(
        LocationType,
        related_name='locations',
        blank=True
    )
    
    is_active = models.BooleanField(default=True)
    is_reserved = models.BooleanField(default=False)
    res_start_date = models.DateTimeField(null=True, blank=True, help_text="Reservation start date")
    res_end_date = models.DateTimeField(null=True, blank=True, help_text="Reservation end date")
    capacity = models.PositiveIntegerField(help_text="Capacity or size measurement")
    
    # Ownership relations
    @property
    def primary_owner(self):
        """Get the primary owner of this location"""
        # Use prefetched data if available
        if hasattr(self, '_prefetched_ownership_shares'):
            primary_share = next((share for share in self._prefetched_ownership_shares if share.is_primary_owner), None)
            return primary_share.contact if primary_share else None
        
        # Fallback to database query
        primary_share = self.ownership_shares.filter(is_primary_owner=True).first()
        return primary_share.contact if primary_share else None
        
    @property
    def primary_owner_percentage(self):
        """Get the primary owner's percentage"""
        # Use prefetched data if available
        if hasattr(self, '_prefetched_ownership_shares'):
            primary_share = next((share for share in self._prefetched_ownership_shares if share.is_primary_owner), None)
            return primary_share.percentage if primary_share else 0
        
        # Fallback to database query
        primary_share = self.ownership_shares.filter(is_primary_owner=True).first()
        return primary_share.percentage if primary_share else 0
    
    @property
    def company_ownership(self):
        """Get the company's ownership details for this location"""
        from contacts.models.contacts import OurCompany
        company = OurCompany.get_instance()
        return {
            'entity': company,
            'name': company.name,
            'percentage': float(self.get_our_Percentage())
        }

    shared_with = models.ManyToManyField(
        Contact,
        through='PartnerShare',
        related_name='shared_locations'
    )
    
    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='created_locations'
    )
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='updated_locations'
    )
    
    # Soft delete field
    is_deleted = models.BooleanField(default=False)
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        """Override save method for custom functionality"""
        # Call the original save method
        super().save(*args, **kwargs)
    

    def create_history_record(self, data, modified_by):
        """Create a history record with specific data"""
        if modified_by is not None:
            LocationHistory.objects.create(
                location=self,
                modified_by=modified_by,
                data=data
            )
    
    def soft_delete(self, user):
        """Mark location as deleted without removing from database"""
        # Save previous state before deletion
        previous_data = self.to_dict()
        
        self.is_deleted = True
        super().save(update_fields=['is_deleted'])
        
        # Create history record with previous data
        self.create_history_record(previous_data, user)
        return True
    
    def to_dict(self):
        """Convert location to dictionary for history tracking"""
        # Use prefetched data if available
        if hasattr(self, '_prefetched_ownership_shares'):
            ownership_shares_data = [
                {
                    'id': str(share.id),
                    'contact_id': str(share.contact.id),
                    'contact_name': share.contact.name,
                    'percentage': float(share.percentage),
                    'is_primary_owner': share.is_primary_owner
                }
                for share in self._prefetched_ownership_shares
            ]
        else:
            # Fallback to database query with select_related
            ownership_shares_data = [
                {
                    'id': str(share.id),
                    'contact_id': str(share.contact.id),
                    'contact_name': share.contact.name,
                    'percentage': float(share.percentage),
                    'is_primary_owner': share.is_primary_owner
                }
                for share in self.ownership_shares.select_related('contact').all()
            ]

        # Use prefetched types if available
        if hasattr(self, '_prefetched_types'):
            types_data = [{'id': str(t.id), 'name': t.name} for t in self._prefetched_types]
        else:
            types_data = [{'id': str(t.id), 'name': t.name} for t in self.types.all()]

        result = {
            'id': str(self.id),
            'name': self.name,
            'description': self.description,
            'address': self.address,
            'is_active': self.is_active,
            'is_reserved': self.is_reserved,
            'res_start_date': self.res_start_date.isoformat() if self.res_start_date else None,
            'res_end_date': self.res_end_date.isoformat() if self.res_end_date else None,
            'capacity': self.capacity,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'created_by': self.created_by.email if self.created_by else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updated_by.email if self.updated_by else None,
            'types': types_data,
            'ownership_shares': ownership_shares_data
        }
        
        # Add our_percentage only if it exists in the model 
        if hasattr(self, 'our_percentage'):
            result['our_percentage'] = float(self.our_percentage)
        
        return result

    def are_we_owners(self):
        """Check if the company is an owner of this location and if it is the primary owner"""
        # Use prefetched data if available
        if hasattr(self, '_prefetched_ownership_shares'):
            return any(share.our_company and share.is_primary_owner for share in self._prefetched_ownership_shares)
        
        # Fallback to database query
        return self.ownership_shares.filter(our_company=True, is_primary_owner=True).exists()

    def get_our_Percentage(self):
        """Get the company's ownership percentage for this location"""
        # Use prefetched data if available
        if hasattr(self, '_prefetched_ownership_shares'):
            company_share = next((share for share in self._prefetched_ownership_shares if share.our_company), None)
            return float(company_share.percentage) if company_share else 0.0
        
        # Fallback to database query
        company_share = self.ownership_shares.filter(our_company=True).first()
        return float(company_share.percentage) if company_share else 0.0
    
    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_reserved']),
            models.Index(fields=['is_deleted']),
        ]


class PartnerShare(models.Model):
    """Model to track ownership percentages for locations shared among contacts"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    location = models.ForeignKey(
        Location, 
        on_delete=models.CASCADE,
        related_name='ownership_shares'
    )
    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        related_name='location_shares'
    )
    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        help_text="Ownership percentage for this contact"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    is_primary_owner = models.BooleanField(default=False)
    our_company = models.BooleanField(default=False)  # New field

    
    class Meta:
        unique_together = ['location', 'contact']
        ordering = ['-percentage']
        indexes = [
            models.Index(fields=['location', 'contact']),
            models.Index(fields=['location', 'is_primary_owner']),
            models.Index(fields=['contact', 'is_primary_owner']),
            models.Index(fields=['our_company']),
        ]
    
    def __str__(self):
        return f"{self.contact.name}: {self.percentage}% of {self.location.name}"


class LocationHistory(models.Model):
    """Model to track location change history"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    location = models.ForeignKey(
        Location,
        on_delete=models.CASCADE,
        related_name='history'
    )
    modified_at = models.DateTimeField(auto_now_add=True)
    modified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='location_modifications'            
    )
    # Store the location data as JSON
    data = models.JSONField()
    
    class Meta:
        ordering = ['-modified_at']
        verbose_name_plural = 'Location histories'
    
    def __str__(self):
        return f"History for {self.location.name} at {self.modified_at}"
