from rest_framework import serializers
from locations.models.locations import Location, LocationType, PartnerShare, LocationHistory
from contacts.serializers.contacts import ContactSimpleSerializer
from users.serializers.users import UserDetailSerializer
from contacts.models.contacts import Contact, OurCompany
from reservations.models.reservations import Reservation
from decimal import Decimal

class LocationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = LocationType
        fields = ['id', 'name', 'created_at']
        read_only_fields = ['id', 'created_at']

class PartnerShareSerializer(serializers.ModelSerializer):
    contact_id = serializers.UUIDField(write_only=True)
    contact = ContactSimpleSerializer(read_only=True)
    is_primary_owner = serializers.BooleanField(default=False)
    class Meta:
        model = PartnerShare
        fields = ['id', 'contact_id', 'contact', 'percentage', 'is_primary_owner']
        read_only_fields = ['id']
    
    def validate_percentage(self, value):
        if value <= 0 or value > 100:
            raise serializers.ValidationError("Percentage must be between 0 and 100")
        return value

class LocationCreateSerializer(serializers.ModelSerializer):

    ownership_shares = PartnerShareSerializer(many=True, required=False)
    type_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        write_only=True
    )
    our_percentage = serializers.DecimalField(
        max_digits=5, decimal_places=2, required=False, write_only=True
    )
    
    class Meta:
        model = Location
        fields = [
            'name', 'description', 'address', 'type_ids', 
            'is_active', 'is_reserved', 'capacity', 
            'ownership_shares', 'is_deleted', 'our_percentage'
        ]
        extra_kwargs = {
            'name': {'required': True},
            'address': {'required': True},
            'capacity': {'required': True},
        }
    
    def validate(self, data):
        # Validate type_ids exist if provided
        type_ids = data.pop('type_ids', [])
        if type_ids:
            for type_id in type_ids:
                try:
                    LocationType.objects.get(pk=type_id)
                except LocationType.DoesNotExist:
                    raise serializers.ValidationError({"type_ids": f"Location type with ID {type_id} does not exist"})
                    
        # Store the validated IDs for use in create/update
        self.validated_type_ids = type_ids
        
        our_percentage = data.get('our_percentage', Decimal('0.0'))
        if isinstance(our_percentage, float):
            our_percentage = Decimal(str(our_percentage))
        if our_percentage < 0 or our_percentage > 100:
            raise serializers.ValidationError({
                "our_percentage": "Company percentage must be between 0 and 100"
            })
        
        # Check consistency of ownership percentages and primary owner
        ownership_shares = data.get('ownership_shares', [])
        total_partner_percentage = sum(
            Decimal(str(share_data.get('percentage', 0))) 
            for share_data in ownership_shares
        )
        if total_partner_percentage + our_percentage > Decimal('100'):
            raise serializers.ValidationError({
                "ownership_shares": "Total ownership percentages exceed 100%"
            })
                
        # Ensure exactly one primary owner in the shares
        primary_owner_count = sum(
            1 for share_data in ownership_shares 
            if share_data.get('is_primary_owner', False)
        )
            
        # Remove the strict validation: allow zero primary owners here, will handle in create()
        if primary_owner_count > 1:
            raise serializers.ValidationError({
                "ownership_shares": "Only one partner can be designated as the primary owner"
            })
        
        return data
    
    def validate_owned_by_id(self, value):
        try:
            Contact.objects.get(pk=value)
            return value
        except Contact.DoesNotExist:
            raise serializers.ValidationError("Contact does not exist")
            
    def validate_our_percentage(self, value):
        if value < 0 or value > 100:
            raise serializers.ValidationError("Percentage must be between 0 and 100")
        return value
    
    def create(self, validated_data):
        ownership_shares_data = validated_data.pop('ownership_shares', [])
        our_percentage = validated_data.pop('our_percentage', Decimal('0.0'))
        if isinstance(our_percentage, float):
            our_percentage = Decimal(str(our_percentage))
        
        # Get pre-fetched objects from validated_data if they exist
        contacts_map = validated_data.pop('contacts_map', {})
        company = validated_data.pop('company', None)
        location_types = validated_data.pop('location_types', None)
        
        # Get company contact from the contacts map or create it
        company_contact = None
        if company and company.id:
            company_id = str(company.id)
            if company_id in contacts_map:
                company_contact = contacts_map[company_id]
            else:
                company_contact, created = Contact.objects.get_or_create(
                    id=company.id,
                    defaults={
                        "name": company.name,
                        "email": company.email
                    }
                )

        # Check if any share is designated as primary owner
        any_primary_owner = any(share.get('is_primary_owner', False) for share in ownership_shares_data)
        
        # Create the location
        location = Location.objects.create(
            name=validated_data['name'],
            description=validated_data.get('description', ''),
            address=validated_data['address'],
            is_active=validated_data.get('is_active', True),
            is_reserved=validated_data.get('is_reserved', False),
            capacity=validated_data['capacity'],
            is_deleted=validated_data.get('is_deleted', False),
            created_by=validated_data.get('created_by'),
            updated_by=validated_data.get('updated_by')
        )
        
        # Create ownership shares using the pre-fetched contacts
        for share_data in ownership_shares_data:
            contact_id = str(share_data.pop('contact_id'))
            contact = contacts_map.get(contact_id)
            
            if contact:
                PartnerShare.objects.create(
                    location=location,
                    contact=contact,
                    percentage=Decimal(str(share_data['percentage'])),
                    is_primary_owner=share_data.get('is_primary_owner', False),
                    our_company=False
                )

        # Add company share if applicable
        if our_percentage > 0 and company_contact:
            PartnerShare.objects.create(
                location=location,
                contact=company_contact,
                percentage=our_percentage,
                is_primary_owner=(not any_primary_owner),
                our_company=True
            )

        # Set location types if provided using pre-fetched types
        if location_types:
            location.types.set(location_types)
        elif hasattr(self, 'validated_type_ids') and self.validated_type_ids:
            types = LocationType.objects.filter(id__in=self.validated_type_ids)
            location.types.set(types)

        return location
    
    def update(self, instance, validated_data):
        if 'ownership_shares' in validated_data:
            ownership_shares_data = validated_data.pop('ownership_shares')
            
            # Clear existing shares if we're replacing them
            instance.ownership_shares.all().delete()
            
            # Create new shares
            for share_data in ownership_shares_data:
                contact_id = share_data.pop('contact_id')
                try:
                    contact = Contact.objects.get(pk=contact_id)
                    PartnerShare.objects.create(
                        location=instance,
                        contact=contact,
                        percentage=share_data['percentage'],
                        is_primary_owner=share_data.get('is_primary_owner', False)
                    )
                except Contact.DoesNotExist:
                    # Skip invalid contacts
                    continue
            
            # Update our_percentage from company share
            company = OurCompany.get_instance()
            company_share = next((s for s in ownership_shares_data if s['contact'] == company), None)
            if company_share:
                instance.our_percentage = company_share['percentage']
        
        # Update types if provided
        if hasattr(self, 'validated_type_ids'):
            types = LocationType.objects.filter(id__in=self.validated_type_ids)
            instance.types.set(types)
            
        # Update remaining fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
            
        instance.save()
        return instance

class LocationHistorySerializer(serializers.ModelSerializer):
    modified_by = UserDetailSerializer(read_only=True)
    
    class Meta:
        model = LocationHistory
        fields = ['id', 'modified_at', 'modified_by', 'data']
        read_only_fields = fields

class LocationMinimalSerializer(serializers.ModelSerializer):
    city = serializers.SerializerMethodField()
    country = serializers.SerializerMethodField()
    areWeOwner = serializers.SerializerMethodField()
    class Meta:
        model = Location
        fields = ['id', 'name', 'address', 'city', 'country' , 'areWeOwner']
    
    def get_areWeOwner(self, obj):
        # Check if the current user is the owner of the location
        return obj.are_we_owners()

    def get_city(self, obj):
        return obj.city.name if hasattr(obj, 'city') and obj.city else ""

    def get_country(self, obj):
        return obj.country.name if hasattr(obj, 'country') and obj.country else ""

class LocationSimpleSerializer(serializers.ModelSerializer):
    type = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    ourPercentage = serializers.SerializerMethodField()
    primaryOwner = serializers.SerializerMethodField()
    sharedWith = serializers.SerializerMethodField()
    ownershipShares = serializers.SerializerMethodField()
    reservedBy = serializers.SerializerMethodField()
    reservedUntil = serializers.SerializerMethodField()
    reservedFrom = serializers.SerializerMethodField()
    lastlyReservedIn = serializers.SerializerMethodField()
    createdAt = serializers.DateTimeField(source='created_at', format='%Y-%m-%d')
    createdBy = serializers.SerializerMethodField()
    contactPerson = serializers.SerializerMethodField()
    contactEmail = serializers.SerializerMethodField()
    contactPhone = serializers.SerializerMethodField()
    areWeOwner = serializers.SerializerMethodField()
    weHaveShares = serializers.SerializerMethodField()
    reservations = serializers.SerializerMethodField()
    res_start_date = serializers.SerializerMethodField()
    res_end_date = serializers.SerializerMethodField()

    class Meta:
        model = Location
        fields = [
            'id', 'name', 'address', 'status', 'capacity', 'areWeOwner', 'weHaveShares',
            'ourPercentage', 'primaryOwner', 'sharedWith', 'ownershipShares',
            'reservedBy', 'reservedUntil', 'reservedFrom', 'lastlyReservedIn', 'createdAt',
            'createdBy', 'contactPerson', 'contactEmail', 'contactPhone', 'reservations', 'description',  
            'type', 'is_active', 'is_reserved', 'is_deleted', 'res_start_date', 'res_end_date'
        ]

    def get_weHaveShares(self, obj):
        """Check if we have any ownership shares in this location"""
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            return any(share['our_company'] for share in obj._ownership_shares_data)
        # Fallback
        shares = getattr(obj, 'ownership_shares_list', None)
        if shares is not None:
            return any(getattr(share, 'our_company', False) for share in shares)
        else:
            return obj.ownership_shares.filter(our_company=True).exists()

    def get_res_start_date(self, obj):
        """Return the reservation start date in YYYY-MM-DD format"""
        if obj.res_start_date:
            return obj.res_start_date.strftime('%Y-%m-%d %H:%M')
        return None

    def get_res_end_date(self, obj):
        """Return the reservation end date in YYYY-MM-DD format"""
        if obj.res_end_date:
            return obj.res_end_date.strftime('%Y-%m-%d %H:%M')
        return None

    def get_areWeOwner(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            return any(share['our_company'] and share['is_primary_owner'] for share in obj._ownership_shares_data)
        # Fallback
        return obj.are_we_owners()

    def get_type(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_location_types_data'):
            types = obj._location_types_data
            return types[0]['name'] if types else ""
        # Fallback
        types = obj.types.all()
        return types[0].name if len(types) > 0 else ""

    def get_primaryOwner(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            primary_share = next((share for share in obj._ownership_shares_data if share['is_primary_owner']), None)
            if primary_share and primary_share['contact']:
                return {
                    "id": str(primary_share['contact']['id']),
                    "name": primary_share['contact']['name'],
                    "percentage": float(primary_share['percentage'])
                }
        else:
            # Fallback
            shares = getattr(obj, 'ownership_shares_list', None)
            if shares is not None:
                primary_share = next((share for share in shares if share.is_primary_owner), None)
            else:
                primary_share = obj.ownership_shares.filter(is_primary_owner=True).first()
            if primary_share and primary_share.contact:
                return {
                    "id": str(primary_share.contact.id),
                    "name": primary_share.contact.name,
                    "percentage": float(primary_share.percentage)
                }
        return None

    def get_status(self, obj):
        if obj.is_deleted:
            return "deleted"
        return "active" if obj.is_active else "inactive"

    def get_sharedWith(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            return [share['contact']['name'] for share in obj._ownership_shares_data if share['contact']]
        # Fallback
        shares = getattr(obj, 'ownership_shares_list', None)
        if shares is not None:
            return [share.contact.name for share in shares if share.contact]
        else:
            return [share.contact.name for share in obj.ownership_shares.all()]

    def get_ownershipShares(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            filtered_shares = [share for share in obj._ownership_shares_data if not share['our_company'] and share['contact']]
            return [{
                "name": share['contact']['name'],
                "percentage": float(share['percentage'])
            } for share in filtered_shares]
        # Fallback
        shares = getattr(obj, 'ownership_shares_list', None)
        if shares is not None:
            filtered_shares = [share for share in shares if not getattr(share, 'our_company', False) and share.contact]
        else:
            filtered_shares = [share for share in obj.ownership_shares.all() if not getattr(share, 'our_company', False) and share.contact]
        return [{
            "name": share.contact.name,
            "percentage": float(share.percentage)
        } for share in filtered_shares]

    def get_reservedBy(self, obj):
        reservations_list = getattr(obj, 'reservations_list', None)
        if reservations_list:
            # Handle both raw SQL dictionary format and Django model objects
            active_reservations = []
            for r in reservations_list:
                if hasattr(r, 'get'):  # Dictionary format
                    if r.get('status') == 'active':
                        active_reservations.append(r)
                else:  # Django model object format
                    if r.status == 'active':
                        active_reservations.append(r)
            
            if active_reservations:
                res = active_reservations[0]
                if hasattr(res, 'get'):  # Dictionary format
                    return res['contact']['name']
                else:  # Django model object format
                    return res.contact.name if res.contact else ""
        return ""

    def get_reservedUntil(self, obj):
        reservations_list = getattr(obj, 'reservations_list', None)
        if reservations_list:
            # Handle both raw SQL dictionary format and Django model objects
            active_reservations = []
            for r in reservations_list:
                if hasattr(r, 'get'):  # Dictionary format
                    if r.get('status') == 'active':
                        active_reservations.append(r)
                else:  # Django model object format
                    if r.status == 'active':
                        active_reservations.append(r)
            
            if active_reservations:
                res = active_reservations[0]
                if hasattr(res, 'get'):  # Dictionary format
                    end_date = res['end_date']
                    if isinstance(end_date, str):
                        return end_date.split('T')[0]
                    return end_date.strftime('%Y-%m-%d')
                else:  # Django model object format
                    return res.end_date.strftime('%Y-%m-%d') if res.end_date else ""
        return ""

    def get_reservedFrom(self, obj):
        reservations_list = getattr(obj, 'reservations_list', None)
        if reservations_list:
            # Handle both raw SQL dictionary format and Django model objects
            active_reservations = []
            for r in reservations_list:
                if hasattr(r, 'get'):  # Dictionary format
                    if r.get('status') == 'active':
                        active_reservations.append(r)
                else:  # Django model object format
                    if r.status == 'active':
                        active_reservations.append(r)
            
            if active_reservations:
                res = active_reservations[0]
                if hasattr(res, 'get'):  # Dictionary format
                    start_date = res['start_date']
                    if isinstance(start_date, str):
                        return start_date.split('T')[0]
                    return start_date.strftime('%Y-%m-%d')
                else:  # Django model object format
                    return res.start_date.strftime('%Y-%m-%d') if res.start_date else ""
        return ""

    def get_lastlyReservedIn(self, obj):
        reservations_list = getattr(obj, 'reservations_list', None)
        if reservations_list and len(reservations_list) > 0:
            res = reservations_list[0]
            if hasattr(res, 'get'):  # Dictionary format
                start_date = res['start_date']
                if isinstance(start_date, str):
                    return start_date.split('T')[0]
                return start_date.strftime('%Y-%m-%d')
            else:  # Django model object format
                return res.start_date.strftime('%Y-%m-%d') if res.start_date else ""
        return ""

    def get_reservations(self, obj):
        reservations_list = getattr(obj, 'reservations_list', None)
        if reservations_list and len(reservations_list) > 0:
            result = []
            for res in reservations_list:
                if hasattr(res, 'get'):  # Dictionary format
                    result.append({
                        "id": str(res['id']),
                        "clientId": str(res['contact']['id']),
                        "clientName": res['contact']['name'],
                        "capacity": res.get('required_capacity', 0),
                        "startDate": res['start_date'].split('T')[0] if isinstance(res['start_date'], str) else res['start_date'].strftime('%Y-%m-%d'),
                        "endDate": res['end_date'].split('T')[0] if isinstance(res['end_date'], str) else res['end_date'].strftime('%Y-%m-%d'),
                        "status": res['status']
                    })
                else:  # Django model object format
                    result.append({
                        "id": str(res.id),
                        "clientId": str(res.contact.id) if res.contact else "",
                        "clientName": res.contact.name if res.contact else "",
                        "capacity": res.required_capacity or 0,
                        "startDate": res.start_date.strftime('%Y-%m-%d') if res.start_date else "",
                        "endDate": res.end_date.strftime('%Y-%m-%d') if res.end_date else "",
                        "status": res.status
                    })
            return result
        return []

    def get_ourPercentage(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            company_share = next((share for share in obj._ownership_shares_data if share['our_company']), None)
            return float(company_share['percentage']) if company_share else 0.0
        # Fallback
        shares = getattr(obj, 'ownership_shares_list', None)
        if shares is not None:
            company_share = next((share for share in shares if getattr(share, 'our_company', False)), None)
            return float(company_share.percentage) if company_share else 0.0
        else:
            company_share = obj.ownership_shares.filter(our_company=True).first()
            return float(company_share.percentage) if company_share else 0.0

    def to_representation(self, obj):
        data = super().to_representation(obj)
        # Remove 'our_percentage' if present (legacy field)
        data.pop('our_percentage', None)
        return data

    def get_createdBy(self, obj):
        return obj.created_by.email if obj.created_by else ""

    def get_contactPerson(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            primary_share = next(
                (share for share in obj._ownership_shares_data if share['is_primary_owner'] and not share['our_company']),
                None
            )
            if primary_share and primary_share['contact']:
                return primary_share['contact']['name']
        else:
            # Fallback
            shares = getattr(obj, 'ownership_shares_list', None)
            if shares is not None:
                primary_share = next(
                    (share for share in shares if share.is_primary_owner and not getattr(share, 'our_company', False)),
                    None
                )
            else:
                primary_share = obj.ownership_shares.filter(is_primary_owner=True, our_company=False).first()
            if primary_share and primary_share.contact:
                return primary_share.contact.name
        return ""

    def get_contactEmail(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            primary_share = next(
                (share for share in obj._ownership_shares_data if share['is_primary_owner'] and not share['our_company']),
                None
            )
            if primary_share and primary_share['contact']:
                return primary_share['contact'].get('email', '')
        else:
            # Fallback
            shares = getattr(obj, 'ownership_shares_list', None)
            if shares is not None:
                primary_share = next(
                    (share for share in shares if share.is_primary_owner and not getattr(share, 'our_company', False)),
                    None
                )
            else:
                primary_share = obj.ownership_shares.filter(is_primary_owner=True, our_company=False).first()
            if primary_share and primary_share.contact:
                return primary_share.contact.email or ""
        return ""

    def get_contactPhone(self, obj):
        # Use the raw SQL data if available
        if hasattr(obj, '_ownership_shares_data'):
            primary_share = next(
                (share for share in obj._ownership_shares_data if share['is_primary_owner'] and not share['our_company']),
                None
            )
            if primary_share and primary_share['contact']:
                return primary_share['contact'].get('phone', '')
        else:
            # Fallback
            shares = getattr(obj, 'ownership_shares_list', None)
            if shares is not None:
                primary_share = next(
                    (share for share in shares if share.is_primary_owner and not getattr(share, 'our_company', False)),
                    None
                )
            else:
                primary_share = obj.ownership_shares.filter(is_primary_owner=True, our_company=False).first()
            if primary_share and primary_share.contact:
                return primary_share.contact.phone or ""
        return ""

class LocationOwnershipUpdateSerializer(serializers.ModelSerializer):
    """Specialized serializer for location updates that handles ownership percentage changes"""
    
    ownership_shares = PartnerShareSerializer(many=True, required=False)
    type_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        write_only=True
    )
    our_percentage = serializers.DecimalField(
        max_digits=5, decimal_places=2, required=False, write_only=True
    )
    
    class Meta:
        model = Location
        fields = [
            'name', 'description', 'address', 'type_ids', 
            'is_active', 'is_reserved', 'capacity', 
            'ownership_shares', 'is_deleted', 'our_percentage'
        ]
    
    def validate(self, data):
        # Validate type_ids exist if provided
        type_ids = data.pop('type_ids', [])
        if type_ids:
            for type_id in type_ids:
                try:
                    LocationType.objects.get(pk=type_id)
                except LocationType.DoesNotExist:
                    raise serializers.ValidationError({"type_ids": f"Location type with ID {type_id} does not exist"})
                    
        # Store the validated IDs for use in update
        self.validated_type_ids = type_ids
        
        our_percentage = data.get('our_percentage', Decimal('0.0'))
        if isinstance(our_percentage, float):
            our_percentage = Decimal(str(our_percentage))
        if our_percentage < 0 or our_percentage > 100:
            raise serializers.ValidationError({
                "our_percentage": "Company percentage must be between 0 and 100"
            })
        
        # Check consistency of ownership percentages
        ownership_shares = data.get('ownership_shares', [])
        total_partner_percentage = sum(
            Decimal(str(share_data.get('percentage', 0))) 
            for share_data in ownership_shares
        )
        if total_partner_percentage + our_percentage > Decimal('100'):
            raise serializers.ValidationError({
                "ownership_shares": "Total ownership percentages exceed 100%"
            })
                
        # Ensure exactly one primary owner in the shares
        primary_owner_count = sum(
            1 for share_data in ownership_shares 
            if share_data.get('is_primary_owner', False)
        )
            
        if primary_owner_count > 1:
            raise serializers.ValidationError({
                "ownership_shares": "Only one partner can be designated as the primary owner"
            })
        
        return data
    
    def update(self, instance, validated_data):
        from decimal import Decimal
        from django.utils import timezone
        from income.models.incomeEvents import Income, IncomeHistory
        from expenses.models.expensesEvents import Event, EventHistory
        from contracts.models.contracts import Contract
        
        # Get old ownership data before update
        old_we_are_owners = instance.are_we_owners()
        old_our_percentage = instance.get_our_Percentage()
        
        # Get old ownership shares for comparison
        old_ownership_shares = {}
        for share in instance.ownership_shares.select_related('contact').all():
            if not share.our_company:
                old_ownership_shares[str(share.contact.id)] = float(share.percentage)
        
        # Update ownership shares if provided
        ownership_shares_updated = False
        new_our_percentage = old_our_percentage
        new_ownership_shares = {}
        
        if 'ownership_shares' in validated_data:
            ownership_shares_data = validated_data.pop('ownership_shares')
            our_percentage = validated_data.pop('our_percentage', Decimal('0.0'))
            if isinstance(our_percentage, float):
                our_percentage = Decimal(str(our_percentage))
            
            new_our_percentage = float(our_percentage)
            ownership_shares_updated = True
            
            # Build new ownership shares map
            for share_data in ownership_shares_data:
                contact_id = str(share_data['contact_id'])
                new_ownership_shares[contact_id] = float(share_data['percentage'])
            
            # Get pre-fetched contacts to avoid duplicate queries
            contact_ids = [str(share_data['contact_id']) for share_data in ownership_shares_data]
            contacts_map = {str(c.id): c for c in Contact.objects.filter(id__in=contact_ids)}
            
            # Get company contact
            from contacts.models.contacts import OurCompany
            company = OurCompany.get_instance()
            company_contact = None
            if company:
                company_contact, created = Contact.objects.get_or_create(
                    id=company.id,
                    defaults={"name": company.name, "email": company.email}
                )
            
            # Clear existing shares
            instance.ownership_shares.all().delete()
            
            # Check if any share is designated as primary owner
            any_primary_owner = any(share.get('is_primary_owner', False) for share in ownership_shares_data)
            
            # Create new shares
            for share_data in ownership_shares_data:
                contact_id = str(share_data.pop('contact_id'))
                contact = contacts_map.get(contact_id)
                
                if contact:
                    PartnerShare.objects.create(
                        location=instance,
                        contact=contact,
                        percentage=Decimal(str(share_data['percentage'])),
                        is_primary_owner=share_data.get('is_primary_owner', False),
                        our_company=False
                    )

            # Add company share if applicable
            if our_percentage > 0 and company_contact:
                PartnerShare.objects.create(
                    location=instance,
                    contact=company_contact,
                    percentage=our_percentage,
                    is_primary_owner=(not any_primary_owner),
                    our_company=True
                )
        
        # Update types if provided
        if hasattr(self, 'validated_type_ids'):
            types = LocationType.objects.filter(id__in=self.validated_type_ids)
            instance.types.set(types)
            
        # Update remaining fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
            
        instance.save()
        
        # Handle contract-based income recalculations if ownership changed and we are owners
        if ownership_shares_updated and old_we_are_owners:
            current_time = timezone.now()
            
            # Get all active contracts for this location with future end dates
            active_contracts = Contract.objects.filter(
                location=instance,
                is_deleted=False,
                end_date__gt=current_time
            ).select_related('contact')
            
            # Prepare lists for bulk operations
            income_updates = []
            income_history_records = []
            income_creates = []
            income_deletes = []
            
            for contract in active_contracts:
                # Get expenses for this contract with future due dates
                contract_expenses = Event.objects.filter(
                    contract=contract,
                    location=instance,
                    is_deleted=False,
                    due_date__gt=current_time
                ).select_related('contact', 'location')
                
                for expense in contract_expenses:
                    # Get existing income records for this expense
                    existing_incomes = Income.objects.filter(
                        expense=expense,
                        is_deleted=False
                    ).select_related('contact')
                    
                    # Create map of existing incomes by contact_id
                    existing_income_map = {}
                    for income in existing_incomes:
                        if income.contact:
                            existing_income_map[str(income.contact.id)] = income
                    
                    # Determine which contacts should have income records based on new ownership
                    required_contacts = set(new_ownership_shares.keys())
                    existing_contacts = set(existing_income_map.keys())
                    
                    # Contacts to add
                    contacts_to_add = required_contacts - existing_contacts
                    
                    # Contacts to remove
                    contacts_to_remove = existing_contacts - required_contacts
                    
                    # Contacts to update (existing contacts with new percentages)
                    contacts_to_update = required_contacts & existing_contacts
                    
                    # Remove income records for contacts no longer in ownership
                    for contact_id in contacts_to_remove:
                        income_to_delete = existing_income_map[contact_id]
                        previous_data = income_to_delete.to_dict()
                        income_deletes.append(income_to_delete.id)
                        
                        income_history_records.append(
                            IncomeHistory(
                                income=income_to_delete,
                                modified_by=validated_data.get('updated_by'),
                                data=previous_data
                            )
                        )
                    
                    # Create income records for new contacts
                    for contact_id in contacts_to_add:
                        contact = contacts_map.get(contact_id)
                        if contact:
                            percentage = Decimal(str(new_ownership_shares[contact_id]))
                            income_amount = (expense.amount * percentage) / 100
                            
                            income_creates.append(
                                Income(
                                    title=f"Income from {expense.title}",
                                    amount=income_amount,
                                    due_date=expense.due_date,
                                    description=f"Generated income from expense: {expense.title}",
                                    status=Income.IncomeStatus.PENDING,
                                    priority=Income.IncomePriority.MEDIUM,
                                    contact=contact,
                                    location=instance,
                                    expense=expense,
                                    contract=contract,
                                    created_by=validated_data.get('updated_by'),
                                    updated_by=validated_data.get('updated_by'),
                                    is_deleted=False
                                )
                            )
                    
                    # Update existing income records with new amounts
                    for contact_id in contacts_to_update:
                        income_to_update = existing_income_map[contact_id]
                        old_percentage = old_ownership_shares.get(contact_id, 0)
                        new_percentage = new_ownership_shares[contact_id]
                        
                        # Only update if percentage actually changed
                        if old_percentage != new_percentage:
                            previous_data = income_to_update.to_dict()
                            
                            # Calculate new amount based on new percentage
                            new_amount = (expense.amount * Decimal(str(new_percentage))) / 100
                            income_to_update.amount = new_amount
                            income_to_update.updated_by = validated_data.get('updated_by')
                            income_updates.append(income_to_update)
                            
                            income_history_records.append(
                                IncomeHistory(
                                    income=income_to_update,
                                    modified_by=validated_data.get('updated_by'),
                                    data=previous_data
                                )
                            )
            
            # Perform bulk operations
            if income_deletes:
                Income.objects.filter(id__in=income_deletes).update(is_deleted=True)
            
            if income_creates:
                Income.objects.bulk_create(income_creates)
            
            if income_updates:
                Income.objects.bulk_update(income_updates, ['amount', 'updated_by'])
            
            if income_history_records:
                IncomeHistory.objects.bulk_create(income_history_records)
        
        return instance
