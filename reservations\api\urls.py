from django.urls import path
from reservations.api.reservations import (
    create_reservation,
    list_reservations,
    get_reservation,
    update_reservation,
    get_reservation_history,
    create_reservation_with_income,
    list_reservations_by_location,
    list_reservations_by_contact,
    get_reservations_by_location_and_date,
    get_reservation_finances,
    soft_delete_reservation
)

urlpatterns = [
    path('getall/', list_reservations, name='list_reservations'),
    path('create/', create_reservation, name='create_reservation'),
    path('getreservation/<uuid:reservation_id>/', get_reservation, name='get_reservation'),
    path('updatereservation/<uuid:reservation_id>/', update_reservation, name='update_reservation'),
    path('history/<uuid:reservation_id>/', get_reservation_history, name='get_reservation_history'),
    path('by-location-and-date/', get_reservations_by_location_and_date, name='get_reservations_by_location_and_date'),
    path('reservation-finances/', get_reservation_finances, name='get_reservation_finances'),
    # New endpoint for creating reservation with income events
    path('create-with-income/', create_reservation_with_income, name='create_reservation_with_income'),
    
    path('by-location/', list_reservations_by_location, name='reservations_by_location'),
    path('by-contact/', list_reservations_by_contact, name='reservations_by_contact'),
    
    path('soft-delete/', soft_delete_reservation, name='soft_delete_reservation'),
]
