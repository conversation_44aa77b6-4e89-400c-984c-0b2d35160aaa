from rest_framework import serializers
from ..models.permissions_base import UserAccess
from ..models.permissions import ModulePermissions

class BasePermissionSerializer(serializers.Serializer):
    """
    Base serializer that defines all common permission fields
    """
    sidebar = serializers.BooleanField(default=False)
    view = serializers.BooleanField(default=False)
    create = serializers.BooleanField(default=False)
    edit = serializers.BooleanField(default=False)
    delete = serializers.BooleanField(default=False)
    export = serializers.BooleanField(default=False)
    import_data = serializers.BooleanField(default=False, source='import')
    approve = serializers.BooleanField(default=False)
    reject = serializers.BooleanField(default=False)
    analytics = serializers.BooleanField(default=False)
    notifications = serializers.BooleanField(default=False)
    view_history = serializers.BooleanField(default=False)

    def update(self, instance, validated_data):
        """
        Update the permission instance with validated data
        """
        for field, value in validated_data.items():
            setattr(instance, field, value)
        instance.save()
        return instance

def create_module_serializer(permission_model):
    """
    Factory function to create serializer classes for each permission module
    """
    extra_fields = {}
    
    # Add module-specific fields
    if permission_model == ModulePermissions['users']:
        extra_fields = {
            'manage_accounts': serializers.BooleanField(default=False),
            'view_activity_log': serializers.BooleanField(default=False)
        }
    elif permission_model == ModulePermissions['contracts']:
        extra_fields = {
            'view_terms': serializers.BooleanField(default=False),
            'manage_templates': serializers.BooleanField(default=False)
        }
    
    class_name = f"{permission_model.__name__}Serializer"
    
    return type(class_name, (BasePermissionSerializer,), {
        **extra_fields,
        'Meta': type('Meta', (), {
            'ref_name': class_name
        })
    })

# Dynamically create serializers for all permission modules
ReservationsPermissionsSerializer = create_module_serializer(ModulePermissions['reservations'])
DashboardPermissionsSerializer = create_module_serializer(ModulePermissions['dashboard'])
FinancialsPermissionsSerializer = create_module_serializer(ModulePermissions['financials'])
ContactsPermissionsSerializer = create_module_serializer(ModulePermissions['contacts'])
SettingsPermissionsSerializer = create_module_serializer(ModulePermissions['settings'])
UsersPermissionsSerializer = create_module_serializer(ModulePermissions['users'])
ContractsPermissionsSerializer = create_module_serializer(ModulePermissions['contracts'])
LocationsPermissionsSerializer = create_module_serializer(ModulePermissions['locations'])
IncomePermissionsSerializer = create_module_serializer(ModulePermissions['income'])
ExpensesPermissionsSerializer = create_module_serializer(ModulePermissions['expenses'])
CalendarPermissionsSerializer = create_module_serializer(ModulePermissions['calendar'])

class UserPermissionsSerializer(serializers.ModelSerializer):
    """
    Main serializer that nests all module permissions under a user
    """
    reservations = ReservationsPermissionsSerializer()
    dashboard = DashboardPermissionsSerializer()
    financials = FinancialsPermissionsSerializer()
    contacts = ContactsPermissionsSerializer()
    settings = SettingsPermissionsSerializer()
    users = UsersPermissionsSerializer()
    contracts = ContractsPermissionsSerializer()
    locations = LocationsPermissionsSerializer()
    income = IncomePermissionsSerializer()
    expenses = ExpensesPermissionsSerializer()
    calendar = CalendarPermissionsSerializer()

    class Meta:
        model = UserAccess
        fields = [
            'reservations', 'dashboard', 'financials', 'contacts',
            'settings', 'users', 'contracts', 'locations',
            'income', 'expenses', 'calendar'
        ]
        read_only_fields = ['id', 'user_id']

    def update(self, instance, validated_data):
        """
        Corrected update method that handles nested permission updates
        """
        # List of all permission modules
        permission_modules = [
            'reservations', 'dashboard', 'financials', 'contacts',
            'settings', 'users', 'contracts', 'locations',
            'income', 'expenses', 'calendar'
        ]

        for module_name in permission_modules:
            if module_name in validated_data:
                module_data = validated_data[module_name]
                perm_instance = getattr(instance, module_name)
                
                # Get the appropriate serializer
                serializer_class = globals()[f"{perm_instance.__class__.__name__}Serializer"]
                serializer = serializer_class(perm_instance, data=module_data, partial=False)
                
                if serializer.is_valid(raise_exception=True):
                    serializer.save()

        return instance

class UserPermissionsLightSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for login endpoint that retrieves
    all permission data with minimal processing
    """
    class Meta:
        model = UserAccess
        fields = [
            'reservations', 'dashboard', 'financials', 'contacts',
            'settings', 'users', 'contracts', 'locations',
            'income', 'expenses', 'calendar'
        ]
    
    def to_representation(self, instance):
        """
        Override to_representation to get permissions directly
        without going through nested serializers
        """
        data = {}
        
        # Flatten permission structure for all modules at once
        modules = [
            'reservations', 'dashboard', 'financials', 'contacts',
            'settings', 'users', 'contracts', 'locations',
            'income', 'expenses', 'calendar'
        ]
        
        for module_name in modules:
            print('modules', instance)
            perm_obj = getattr(instance, module_name)
            module_data = {}
            
            # Get all field values directly from the permission object
            for field in perm_obj._meta.fields:
                if field.name != 'id' and field.name != 'user_access':
                    module_data[field.name] = getattr(perm_obj, field.name)
            
            data[module_name] = module_data
            
        return data