import uuid
from django.db import models
from django.utils import timezone
from contacts.models.contacts import Contact
from locations.models.locations import Location
from users.models.users import User
from contracts.models.contracts import Contract
from reservations.models.reservations import Reservation
class EventType(models.Model):
    """Model for expense event types"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_event_types'
    )
    
    def __str__(self):
        return self.name
    
    class Meta:
        ordering = ['name']

class Event(models.Model):
    """Model for expense events"""
    
    class EventStatus(models.TextChoices):
        COMPLETED = 'completed', 'Completed'
        PENDING = 'pending', 'Pending'
        CANCELLED = 'cancelled', 'Cancelled'
        UPCOMING = 'upcoming', 'Upcoming'
        OVERDUE = 'overdue', 'Overdue'
    
    class EventPriority(models.TextChoices):
        LOW = 'low', 'Low'
        MEDIUM = 'medium', 'Medium'
        HIGH = 'high', 'High'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    due_date = models.DateTimeField()
    paid_date = models.DateTimeField(null=True, blank=True)
    description = models.TextField(blank=True, null=True)
    status = models.CharField(
        max_length=20,
        choices=EventStatus.choices,
        default=EventStatus.PENDING
    )
    priority = models.CharField(
        max_length=10,
        choices=EventPriority.choices,
        default=EventPriority.MEDIUM
    )
    
    # New field for event type
    type = models.ForeignKey(
        EventType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='events'
    )
    
    # New field for contract
    contract = models.ForeignKey(
        Contract,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='expense_events'
    )
    
    # New field for parent event
    parent = models.ForeignKey(
        'expenses.ParentEvent',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='child_events'
    )
    
    # Relationships
    contact = models.ForeignKey(Contact, on_delete=models.SET_NULL, null=True, blank=True, related_name='expenses_events')
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='expenses_events_locations')

    income = models.ForeignKey(
        'income.Income',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='income_expense_events'
    )
    reservation = models.ForeignKey(
        Reservation,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        default=None,
        related_name='expense_events'
    )
    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_events'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='updated_events'
    )
    
    # Soft delete field
    is_deleted = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.title} - {self.amount} ({self.get_status_display()})"
    
    def save(self, *args, **kwargs):
        """Override save method for custom functionality"""
        # Auto-update status based on dates
        if self.status not in ['completed', 'cancelled'] and self.due_date:
            now = timezone.now()
            if self.due_date < now:
                self.status = self.EventStatus.OVERDUE
            else:
                self.status = self.EventStatus.UPCOMING
        
        # If paid_date is set, mark as completed
        if self.paid_date and self.status != self.EventStatus.CANCELLED:
            self.status = self.EventStatus.COMPLETED
            
        # Call the original save method
        super().save(*args, **kwargs)
    
    def create_history_record(self, data, modified_by):
        """Create a history record with specific data"""
        if modified_by is not None:
            EventHistory.objects.create(
                event=self,
                modified_by=modified_by,
                data=data
            )
    
    def soft_delete(self, user):
        """Mark event as deleted without removing from database"""
        # Save previous state before deletion
        previous_data = self.to_dict()
        
        self.is_deleted = True
        super().save(update_fields=['is_deleted'])
        
        # Create history record with previous data
        self.create_history_record(previous_data, user)
        return True
    
    def to_dict(self):
        """Convert event to dictionary for history tracking"""
        
        result = {
            'title': self.title,
            'amount': float(self.amount),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'paid_date': self.paid_date.isoformat() if self.paid_date else None,
            'description': self.description,
            'status': self.status,
            'priority': self.priority,
            'contact': {
                'id': str(self.contact.id),
                'name': self.contact.name
            } if self.contact else None,
            'location': {
                'id': str(self.location.id),
                'name': self.location.name
            } if self.location else None,
        }
        
        # Add type information if available
        if self.type:
            result['type_id'] = str(self.type.id)
            result['type_name'] = self.type.name
        else:
            result['type_id'] = None
            result['type_name'] = None
        
        # Add parent information if available
        if self.parent:
            result['parent_id'] = str(self.parent.id)
            result['parent_title'] = self.parent.title
        else:
            result['parent_id'] = None
            result['parent_title'] = None
            
        return result
    
    class Meta:
        ordering = ['-due_date']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['priority']),
            models.Index(fields=['is_deleted']),
        ]


class EventHistory(models.Model):
    """Model to track event change history"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event = models.ForeignKey(
        Event,
        on_delete=models.CASCADE,
        related_name='history'
    )
    modified_at = models.DateTimeField(auto_now_add=True)
    modified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='event_modifications'
    )
    # Store the event data as JSON
    data = models.JSONField()
    
    class Meta:
        ordering = ['-modified_at']
        verbose_name_plural = 'Event histories'
    
    def __str__(self):
        return f"History for {self.event.title} at {self.modified_at}"
