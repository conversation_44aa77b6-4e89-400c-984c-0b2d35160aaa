from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.password_validation import validate_password
from ..models.users import User
from ..serializers.users import UserCreateSerializer
from ..decorators.permDecorators import check_permission

@api_view(['POST'])
def create_user(request):
    serializer = UserCreateSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        validate_password(serializer.validated_data['password'])
    except Exception as e:
        return Response(
            {'password': list(e.messages)}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    user = User.objects.create_user(
        email=serializer.validated_data['email'],
        password=serializer.validated_data['password'],
        role=serializer.validated_data.get('role', User.Role.USER)
    )
    
    return Response({
        'id': user.id,
        'email': user.email,
        'role': user.role
    }, status=status.HTTP_201_CREATED)

@api_view(['GET'])
@permission_classes([IsAdminUser])
def list_users(request):
    users = User.objects.all()
    data = [{
        'id': user.id,
        'email': user.email,
        'role': user.role
    } for user in users]
    return Response(data)