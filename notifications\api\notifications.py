from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.db.models import Q, Prefetch
from django.db.models.expressions import Case, When
from django.db.models import F, Value, <PERSON><PERSON>anField
from django.utils import timezone
from django.core.management import call_command
from users.models.users import User
from notifications.models.notifications import Notification, NotificationType
from notifications.serializers.notifications import (
    NotificationCreateSerializer, 
    NotificationDetailSerializer,
    NotificationTypeSerializer
)
from sarayVera.settings import numOfQueriesWraper

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_notification(request):
    """Create a new notification"""
    try:
        serializer = NotificationCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Create notification
        notification = serializer.save()
        
        # Return detailed notification info
        detail_serializer = NotificationDetailSerializer(
            notification,
            context={'request': request}
        )
        return Response(detail_serializer.data, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": "Failed to create notification", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_notifications(request):
    """Get all notifications with optional filters"""
    try:
        # Fetch notifications with optimized queries and sort with NULLs last
        notifications = Notification.objects.select_related('type').prefetch_related(
            Prefetch('read_by', queryset=User.objects.all(), to_attr='read_by_list')
        ).annotate(
            has_view_date=Case(
                When(view_date__isnull=False, then=Value(True)),
                default=Value(False),
                output_field=BooleanField()
            )
        ).order_by('-has_view_date', '-view_date', '-created_at')
        
        # Filter notifications based on view_date:
        # - Include those with view_date == null
        # - Include those where view_date is in the past or present
        # - Exclude those where view_date is in the future
        current_time = timezone.now()
        notifications = notifications.filter(
            Q(view_date__isnull=True) | 
            Q(view_date__lte=current_time)
        )
        
        # Filter by priority
        priority = request.query_params.get('priority')
        if priority:
            notifications = notifications.filter(priority=priority)
            
        # Filter by type
        type_id = request.query_params.get('type_id')
        if type_id:
            notifications = notifications.filter(type_id=type_id)
        
        # Filter by read status for the current user
        read_status = request.query_params.get('read')
        if read_status is not None:
            if read_status.lower() == 'true':
                notifications = notifications.filter(read_by=request.user)
            elif read_status.lower() == 'false':
                notifications = notifications.exclude(read_by=request.user)
        
        # Search by title or message
        search = request.query_params.get('search')
        if search:
            notifications = notifications.filter(
                Q(title__icontains=search) | 
                Q(title_ar__icontains=search) |
                Q(message__icontains=search) |
                Q(message_ar__icontains=search)
            )
        
        # Check if we have any results
        if not notifications.exists():
            return Response({"notifications": [], "count": 0})
        
        serializer = NotificationDetailSerializer(
            notifications, 
            many=True,
            context={'request': request}
        )
        return Response({"notifications": serializer.data, "count": notifications.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve notifications", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_notification(request, notification_id):
    """Get a notification by ID and mark it as viewed"""
    try:
        # Use select_related and prefetch_related for better performance
        notification = get_object_or_404(
            Notification.objects.select_related('type').prefetch_related(
                Prefetch('read_by', queryset=User.objects.all(), to_attr='read_by_list')
            ),
            id=notification_id
        )
        
        # Update the view_date if not already set
        if notification.view_date is None:
            notification.view_date = timezone.now()
            notification.save(update_fields=['view_date'])
        
        serializer = NotificationDetailSerializer(
            notification,
            context={'request': request}
        )
        return Response(serializer.data)
    except Notification.DoesNotExist:
        return Response(
            {"error": f"Notification with ID {notification_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve notification", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_as_read(request, notification_id):
    """Mark a notification as read by the current user"""
    try:
        notification = get_object_or_404(Notification.objects.select_related('type'), id=notification_id)
        notification.read_by.add(request.user)
        
        # Get the updated notification with prefetched data
        updated_notification = Notification.objects.select_related('type').prefetch_related(
            Prefetch('read_by', queryset=User.objects.all(), to_attr='read_by_list')
        ).get(id=notification_id)
        
        serializer = NotificationDetailSerializer(
            updated_notification,
            context={'request': request}
        )
        return Response(serializer.data)
    except Notification.DoesNotExist:
        return Response(
            {"error": f"Notification with ID {notification_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to mark notification as read", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_as_unread(request, notification_id):
    """Mark a notification as unread by the current user"""
    try:
        notification = get_object_or_404(Notification.objects.select_related('type'), id=notification_id)
        notification.read_by.remove(request.user)
        
        # Get the updated notification with prefetched data
        updated_notification = Notification.objects.select_related('type').prefetch_related(
            Prefetch('read_by', queryset=User.objects.all(), to_attr='read_by_list')
        ).get(id=notification_id)
        
        serializer = NotificationDetailSerializer(
            updated_notification,
            context={'request': request}
        )
        return Response(serializer.data)
    except Notification.DoesNotExist:
        return Response(
            {"error": f"Notification with ID {notification_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to mark notification as unread", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_notification_types(request):
    """List all notification types"""
    try:
        notification_types = NotificationType.objects.all()
        serializer = NotificationTypeSerializer(notification_types, many=True)
        return Response({"notification_types": serializer.data, "count": notification_types.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve notification types", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_notification_type(request):
    """Create a new notification type"""
    try:
        serializer = NotificationTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if type with same name already exists
        name = serializer.validated_data['name']
        if NotificationType.objects.filter(name__iexact=name).exists():
            return Response(
                {"error": f"Notification type '{name}' already exists"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create new notification type
        notification_type = serializer.save(created_by=request.user)
        
        return Response(
            NotificationTypeSerializer(notification_type).data,
            status=status.HTTP_201_CREATED
        )
    except Exception as e:
        return Response(
            {"error": "Failed to create notification type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_notification_type(request, type_id):
    """Delete a notification type"""
    try:
        notification_type = get_object_or_404(NotificationType, id=type_id)
        
        # Check if type is used by any notifications
        if notification_type.notifications.exists():
            return Response(
                {"error": f"Cannot delete notification type '{notification_type.name}' as it is being used by notifications"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Store type name for response
        type_name = notification_type.name
        
        # Delete the type
        notification_type.delete()
        
        return Response({
            "message": f"Notification type '{type_name}' has been deleted"
        }, status=status.HTTP_200_OK)
    except NotificationType.DoesNotExist:
        return Response(
            {"error": f"Notification type with ID {type_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete notification type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def check_due_items(request):
    """Manually trigger check for due items and create notifications"""
    try:
        # Call the management command
        call_command('check_due_items')
        
        return Response({
            "message": "Due items check completed successfully. Notifications created for any due expenses and income."
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {"error": "Failed to check due items", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_unread_count(request):
    """Get count of unread notifications for the current user"""
    try:
        # Only get the count without fetching all notification data
        count = Notification.objects.filter(
            Q(view_date__isnull=True) | Q(view_date__lte=timezone.now())
        ).exclude(read_by=request.user).count()
        
        return Response({"unread_count": count})
    except Exception as e:
        return Response(
            {"error": "Failed to get unread count", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
