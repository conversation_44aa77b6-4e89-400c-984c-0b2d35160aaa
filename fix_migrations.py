import os
import sys
import shutil

def main():
    """
    <PERSON><PERSON><PERSON> to fix migration issues by cleaning up problematic migrations
    and preparing to create fresh migrations.
    """
    # Store the current directory
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Get a list of migration directories to clean
    apps_to_clean = ['expenses', 'income']
    
    for app in apps_to_clean:
        migration_dir = os.path.join(base_dir, app, 'migrations')
        
        # Skip if migrations directory doesn't exist
        if not os.path.exists(migration_dir):
            print(f"Skipping {app}: migrations directory not found.")
            continue
        
        # Create a backup directory
        backup_dir = os.path.join(base_dir, f"{app}_migrations_backup")
        os.makedirs(backup_dir, exist_ok=True)
        
        # Copy migrations to backup
        for filename in os.listdir(migration_dir):
            if filename.endswith('.py') and filename != '__init__.py':
                src_path = os.path.join(migration_dir, filename)
                dst_path = os.path.join(backup_dir, filename)
                shutil.copy2(src_path, dst_path)
                print(f"Backed up: {filename} to {backup_dir}")
        
        # Remove problematic migrations
        for filename in os.listdir(migration_dir):
            if (filename.endswith('.py') and 
                filename != '__init__.py' and 
                ('parent_event' in filename.lower() or 'parent_income' in filename.lower() or
                 'remove_is_deleted' in filename.lower())):
                os.remove(os.path.join(migration_dir, filename))
                print(f"Removed problematic migration: {app}/migrations/{filename}")
    
    print("\nMigration cleanup complete!")
    print("Now run the following commands:")
    print("1. python manage.py makemigrations expenses")
    print("2. python manage.py makemigrations income")
    print("3. python manage.py migrate")

if __name__ == "__main__":
    main()
