from django.urls import path
from django.http import HttpResponse
from locations.api.locations import (
    create_location,
    list_locations,
    get_location,
    update_location,
    get_location_history,
    update_partner_share,
    list_location_types,
    create_location_type,
    delete_location_type,
    get_location_finances,
    soft_delete_location,
    update_location_reservation_for_not_ours
)

urlpatterns = [
    # Existing location endpoints
    path('getall/', list_locations, name='list_locations'),
    path('create/', create_location, name='create_location'),
    path('getlocation/<uuid:location_id>/', get_location, name='get_location'),
    path('updatelocation/<uuid:location_id>/', update_location, name='update_location'),
    path('soft-delete/', soft_delete_location, name='soft_delete_location'),
    path('history/<uuid:location_id>/', get_location_history, name='get_location_history'),
    path('updatelocation/<uuid:location_id>/partner/<uuid:partner_id>/', update_partner_share, name='update_partner_share'),
    path('health/', lambda request: HttpResponse(status=200), name='health_check'),
    # New endpoints for location types
    path('types/', list_location_types, name='list_location_types'),
    path('types/create/', create_location_type, name='create_location_type'),
    path('types/delete/<uuid:type_id>/', delete_location_type, name='delete_location_type'),
    path('location-finances', get_location_finances, name='get_location_finances'),
    # New endpoint for updating reservation dates
    path('update-reservation/<uuid:location_id>/', update_location_reservation_for_not_ours, name='update_location_reservation_for_not_ours'),
]