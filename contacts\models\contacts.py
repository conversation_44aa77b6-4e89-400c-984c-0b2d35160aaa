import uuid
import json
from django.db import models
from django.utils import timezone
from users.models.users import User
from django.db.models import Sum
from decimal import Decimal
from django.core.exceptions import ValidationError

class ContactType(models.Model):
    """Model for contact types"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_contact_types'
    )
    
    def __str__(self):
        return self.name
    
    class Meta:
        ordering = ['name']

class Contact(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    email = models.CharField(null=True, blank=True)
    phone = models.Char<PERSON>ield(max_length=20, null=True, blank=True)
    company = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    address = models.TextField(null=True, blank=True)
    
    types = models.ManyToManyField(
        ContactType,
        related_name='contacts',
        blank=True
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='created_contacts'
    )
    is_deleted = models.BooleanField(default=False)
    
    def __str__(self):
        return self.name
    def calculate_balance(self):
        """Calculate balance for the contact based on incomes and expenses"""
        # Check if there are any income events
        if not self.income_events.exists():
            total_income = Decimal('0.0')
        else:
            total_income = self.income_events.filter(is_deleted=False).aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.0')

        if not self.expenses_events.exists():
            total_expenses = Decimal('0.0')
        else:
            total_expenses = self.expenses_events.filter(is_deleted=False).aggregate(
                total=Sum('amount')
            )['total'] or Decimal('0.0')

        return total_income - total_expenses
    
    def get_owned_locations(self):
        """Retrieve all locations owned by this contact"""
        if not hasattr(self, 'owned_locations'):
            return []
        return self.owned_locations.filter(is_deleted=False)

    def get_shared_locations(self):
        """Retrieve all locations shared with this contact"""
        if not hasattr(self, 'shared_locations'):
            return []
        return self.shared_locations.filter(is_deleted=False)
    
    @property
    def type_list(self):
        """Returns the types as a list of names"""
        # Use prefetched data if available
        if hasattr(self, '_prefetched_types'):
            return [t.name for t in self._prefetched_types]
        
        # Fallback to database query
        return [t.name for t in self.types.all()]
    
    def save(self, *args, **kwargs):
        """Override save method to track history"""
        modified_by = kwargs.pop('modified_by', None) if 'modified_by' in kwargs else None
        super().save(*args, **kwargs)
    
    def create_history_record(self, data, modified_by):
        """Create a history record with specific data"""
        if modified_by is not None:
            ContactHistory.objects.create(
                contact=self,
                modified_by=modified_by,
                data=data
            )
    
    def soft_delete(self, user):
        """Mark contact as deleted instead of actually deleting it"""
        previous_data = self.to_dict()
        self.is_deleted = True
        super().save()
        self.create_history_record(previous_data, user)
        return True
    
    def to_dict(self):
        """Convert contact to dictionary for history tracking"""
        # Use prefetched data if available
        if hasattr(self, '_prefetched_types'):
            types_data = [{
                'id': str(type_obj.id),
                'name': type_obj.name
            } for type_obj in self._prefetched_types]
        else:
            # Fallback to database query
            types_data = [{
                'id': str(type_obj.id),
                'name': type_obj.name
            } for type_obj in self.types.all()]
        
        return {
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'company': self.company,
            'address': self.address,
            'types': types_data,
        }
    
    def set_types_by_ids(self, type_ids):
        """Set contact types by their IDs"""
        if not type_ids:
            self.types.clear()
            return
            
        # Convert string IDs to UUID if needed
        try:
            validated_ids = []
            for type_id in type_ids:
                if isinstance(type_id, str):
                    # Validate UUID format
                    validated_ids.append(uuid.UUID(type_id))
                else:
                    validated_ids.append(type_id)
                    
            # Get existing contact types with these IDs
            contact_types = ContactType.objects.filter(id__in=validated_ids)
            
            # Check if all IDs exist
            if len(contact_types) != len(type_ids):
                missing_ids = set(validated_ids) - set(contact_type.id for contact_type in contact_types)
                raise ValueError(f"Contact types with IDs {missing_ids} not found")
                
            # Clear existing types and add new ones
            self.types.clear()
            self.types.add(*contact_types)
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid contact type IDs: {str(e)}")
    
    def set_types(self, type_names):
        """Set contact types by their names for backwards compatibility"""
        if not type_names:
            self.types.clear()
            return
            
        # Get or create contact types with these names
        contact_types = []
        for name in type_names:
            contact_type, created = ContactType.objects.get_or_create(name=name)
            contact_types.append(contact_type)
            
        # Clear existing types and add new ones
        self.types.clear()
        self.types.add(*contact_types)
    
class ContactHistory(models.Model):
    """Model to track contact change history"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    contact = models.ForeignKey(
        Contact, 
        on_delete=models.CASCADE, 
        related_name='history'
    )
    modified_at = models.DateTimeField(auto_now_add=True)
    modified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='contact_modifications'
    )
    data = models.JSONField()
    
    class Meta:
        ordering = ['-modified_at']
        verbose_name_plural = 'Contact histories'
    
    def __str__(self):
        return f"History for {self.contact.name} at {self.modified_at}"
    
class OurCompany(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    email = models.EmailField()

    # Single-instance enforcement
    def save(self, *args, **kwargs):
        if OurCompany.objects.exists() and not self.pk:
            raise ValidationError("Only one company settings instance is allowed")
        return super().save(*args, **kwargs)

    def __str__(self):
        return self.name
    
    @classmethod
    def get_instance(cls):
        """Get the singleton instance or create it if it doesn't exist"""
        instance, created = cls.objects.get_or_create(
            defaults={
                'name': 'SarayVera',
                'email': '<EMAIL>',
            }
        )
        return instance