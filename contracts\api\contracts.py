from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status as http_status  # Rename status to avoid conflicts
from django.shortcuts import get_object_or_404
from django.db.models import Q, Prefetch
from django.db import transaction
from django.utils import timezone
from django.utils.dateparse import parse_datetime, parse_date
from datetime import timedelta

from contracts.models.contracts import Contract, ContractHistory
from contracts.serializers.contracts import (
    ContractCreateSerializer,
    ContractDetailSerializer,
    ContractSimpleSerializer,
    ContractHistorySerializer,
    ContractListResponseSerializer
)
from income.models.incomeEvents import Income, IncomeHistory
from expenses.models.expensesEvents import Event, EventHistory
from expenses.models.expensesEvents import Event, EventType
from expenses.serializers.expenses import EventCreateSerializer, EventDetailSerializer
from notifications.utils import create_system_notification, delete_notifications_by_category  # Add the missing import
from sarayVera.settings import numOfQueriesWraper
from contracts.models.contracts import ContractDocument
from expenses.api.expenses import create_contracts_events
from locations.models.locations import PartnerShare  # Add this missing import

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_contract(request):
    try:
        with transaction.atomic():
            serializer = ContractCreateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
                
            # Save contract with current user as creator
            contract = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )
            
            # Create notification for new contract
            create_system_notification(
                title="New Contract Created",
                title_ar="تم إنشاء عقد جديد",
                message=f"A new contract '{contract.title}' was created by {request.user.email}",
                message_ar=f"تم إنشاء عقد جديد '{contract.title}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Contract Created",
                category="contract",
                category_id=str(contract.id)
            )
            
            # Create notification for contract renewal if applicable
            if contract.renewal_terms and contract.renewal_terms.notice_period_days:
                notice_period_days = contract.renewal_terms.notice_period_days
                
                if notice_period_days and contract.end_date:
                    notification_date = contract.end_date - timedelta(days=notice_period_days)
                    
                    if notification_date > timezone.now().date():
                        create_system_notification(
                            title="Contract Renewal Notification",
                            title_ar="إشعار تجديد العقد",
                            message=f"The contract '{contract.title}' has a renewal notification set for {notification_date.strftime('%Y-%m-%d')}",
                            message_ar=f"تم تعيين إشعار تجديد العقد '{contract.title}' في {notification_date.strftime('%Y-%m-%d')}",
                            priority="low",
                            view_date=notification_date,
                            notification_type_name="Contract Renewal Notification",
                            category="contract",
                            category_id=str(contract.id)
                        )
            
            # Return detailed contract info
            detail_serializer = ContractDetailSerializer(contract)
            return Response(detail_serializer.data, status=http_status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": "Failed to create contract", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_contracts(request):
    try:
        # Filter out soft-deleted contracts by default
        include_deleted = request.query_params.get('include_deleted', 'false').lower() == 'true'
        
        if include_deleted:
            contracts = Contract.objects.all().order_by('-updated_at')
        else:
            contracts = Contract.objects.filter(is_deleted=False).order_by('-updated_at')
        
        # Add proper select_related and prefetch_related to optimize queries
        contracts = contracts.select_related(
            'contact',
            'location',
            'created_by',
            'updated_by',
            'renewal_terms'
        ).prefetch_related(
            'documents',
            'expense_events',
            Prefetch('contact__types', to_attr='_prefetched_types'),
            Prefetch('location__ownership_shares', 
                     queryset=PartnerShare.objects.select_related('contact'),
                     to_attr='_prefetched_ownership_shares'),
            Prefetch('location__types', to_attr='_prefetched_types')
        )
            
        # Filter by status
        status_param = request.query_params.get('status')
        if status_param:
            contracts = contracts.filter(status=status_param)
            
        # Filter by date range
        start_from = request.query_params.get('start_from')
        if start_from:
            contracts = contracts.filter(start_date__gte=start_from)
            
        end_before = request.query_params.get('end_before')
        if end_before:
            contracts = contracts.filter(end_date__lte=end_before)
            
        # Filter by contact or location
        contact_id = request.query_params.get('contact_id')
        if contact_id:
            contracts = contracts.filter(contact_id=contact_id)
            
        location_id = request.query_params.get('location_id')
        if location_id:
            contracts = contracts.filter(location_id=location_id)
            
        # Search by title, contract number or description
        search = request.query_params.get('search')
        if search:
            contracts = contracts.filter(
                Q(title__icontains=search) | 
                Q(description__icontains=search)
            )
        
        # Execute the query to force evaluation and ensure prefetch works
        contracts = list(contracts)
        
        # Debug: Check if prefetch worked
        if contracts:
            print(f"Debug: First contract contact has _prefetched_types: {hasattr(contracts[0].contact, '_prefetched_types')}")
            
        # Check if we have any results
        if not contracts:
            return Response({"contracts": [], "count": 0})
            
        serializer = ContractListResponseSerializer(contracts, many=True)
        return Response({
            "contracts": serializer.data,
            "count": len(contracts)
        })
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve contracts", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_contract(request, contract_id):
    try:
        contract = get_object_or_404(Contract, id=contract_id)
        serializer = ContractDetailSerializer(contract)
        return Response(serializer.data)
    except Contract.DoesNotExist:
        return Response(
            {"error": f"Contract with ID {contract_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve contract", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_contract(request, contract_id):
    try:
        with transaction.atomic():
            contract = get_object_or_404(Contract, id=contract_id)
            
            # Capture the previous state before making changes
            previous_data = contract.to_dict()
            
            # Extract events and documents data from request
            income_events_data = request.data.get('income_events', [])
            expense_events_data = request.data.get('expense_events', [])
            documents_data = request.data.get('documents', [])
            renewal_terms_data = request.data.get('renewal_terms', {})
            
            # Remove events and documents data from contract data to avoid validation errors
            contract_data = request.data.copy()
            contract_data.pop('income_events', None)
            contract_data.pop('expense_events', None)
            contract_data.pop('documents', None)
            contract_data.pop('renewal_terms', None)
            
            serializer = ContractCreateSerializer(
                contract,
                data=contract_data,
                partial=True
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
            
            # Update with current user as updater
            contract = serializer.save(updated_by=request.user)
            
            # Process renewal terms if provided
            renewal_terms_updated = False
            if renewal_terms_data:
                if contract.renewal_terms:
                    # Update existing renewal terms
                    for field, value in renewal_terms_data.items():
                        if hasattr(contract.renewal_terms, field):
                            setattr(contract.renewal_terms, field, value)
                    contract.renewal_terms.save()
                    renewal_terms_updated = True
                else:
                    # Create new renewal terms
                    from contracts.models.contracts import RenewalTerms
                    renewal_terms = RenewalTerms.objects.create(**renewal_terms_data)
                    contract.renewal_terms = renewal_terms
                    contract.save(update_fields=['renewal_terms'])
                    renewal_terms_updated = True
                
                # Create renewal notification if notice_period_days is set and contract has end_date
                if renewal_terms_updated and contract.renewal_terms and contract.renewal_terms.notice_period_days and contract.end_date:
                    notice_period_days = contract.renewal_terms.notice_period_days
                    notification_date = contract.end_date - timedelta(days=notice_period_days)
                    
                    if notification_date > timezone.now().date():
                        # Delete old renewal notifications for this contract
                        delete_notifications_by_category("contract_renewal", str(contract.id))
                        
                        create_system_notification(
                            title="Contract Renewal Notification Updated",
                            title_ar="تم تحديث إشعار تجديد العقد",
                            message=f"The contract '{contract.title}' has an updated renewal notification set for {notification_date.strftime('%Y-%m-%d')}",
                            message_ar=f"تم تحديث إشعار تجديد العقد '{contract.title}' ليكون في {notification_date.strftime('%Y-%m-%d')}",
                            priority="low",
                            view_date=notification_date,
                            notification_type_name="Contract Renewal Notification",
                            category="contract_renewal",
                            category_id=str(contract.id)
                        )
            
            # Process documents
            created_documents = []
            updated_documents = []
            deleted_documents = []
            
            for doc_data in documents_data:
                doc_id = doc_data.get('id')
                is_deleted = doc_data.get('is_deleted', False)
                
                if doc_id:
                    # Update existing document
                    try:
                        document = ContractDocument.objects.get(id=doc_id, contract=contract)
                        
                        if is_deleted:
                            # Delete the document
                            document.delete()
                            deleted_documents.append(str(doc_id))
                        else:
                            # Update document
                            for field, value in doc_data.items():
                                if hasattr(document, field) and field != 'id':
                                    setattr(document, field, value)
                            document.save()
                            updated_documents.append(str(document.id))
                    except ContractDocument.DoesNotExist:
                        return Response(
                            {"error": f"Document with ID {doc_id} not found or not associated with this contract"},
                            status=http_status.HTTP_404_NOT_FOUND
                        )
                else:
                    # Create new document if not marked as deleted
                    if not is_deleted:
                        new_document = ContractDocument.objects.create(
                            contract=contract,
                            **{k: v for k, v in doc_data.items() if k != 'is_deleted'}
                        )
                        created_documents.append(str(new_document.id))
            
            # Process income events
            created_incomes = []
            updated_incomes = []
            deleted_incomes = []
            
            for income_data in income_events_data:
                income_id = income_data.get('id')
                is_deleted = income_data.get('is_deleted', False)
                
                if income_id:
                    # Update existing income
                    try:
                        income = Income.objects.get(id=income_id, contract=contract)
                        previous_income_data = income.to_dict()
                        
                        if is_deleted:
                            # Soft delete the income
                            income.is_deleted = True
                            income.updated_by = request.user
                            income.save(update_fields=['is_deleted', 'updated_by'])
                            income.create_history_record(previous_income_data, request.user)
                            
                            # Delete old notifications for this income
                            delete_notifications_by_category("income", str(income.id))
                            
                            deleted_incomes.append(str(income.id))
                        else:
                            # Update income
                            from income.serializers.income import IncomeCreateSerializer
                            income_serializer = IncomeCreateSerializer(
                                income,
                                data=income_data,
                                partial=True
                            )
                            if income_serializer.is_valid():
                                updated_income = income_serializer.save(updated_by=request.user)
                                updated_income.create_history_record(previous_income_data, request.user)
                                updated_incomes.append(str(updated_income.id))
                            else:
                                return Response(
                                    {"error": f"Invalid income data for ID {income_id}", "details": income_serializer.errors},
                                    status=http_status.HTTP_400_BAD_REQUEST
                                )
                    except Income.DoesNotExist:
                        return Response(
                            {"error": f"Income with ID {income_id} not found or not associated with this contract"},
                            status=http_status.HTTP_404_NOT_FOUND
                        )
                else:
                    # Create new income if not marked as deleted
                    if not is_deleted:
                        from income.serializers.income import IncomeCreateSerializer
                        # Ensure the income is linked to this contract
                        income_data['contract_id'] = str(contract.id)
                        
                        income_serializer = IncomeCreateSerializer(data=income_data)
                        if income_serializer.is_valid():
                            new_income = income_serializer.save(
                                created_by=request.user,
                                updated_by=request.user
                            )
                            # Link to contract
                            new_income.contract = contract
                            new_income.save()
                            created_incomes.append(str(new_income.id))
                        else:
                            return Response(
                                {"error": "Invalid new income data", "details": income_serializer.errors},
                                status=http_status.HTTP_400_BAD_REQUEST
                            )
            
            # Process expense events
            created_expenses = []
            updated_expenses = []
            deleted_expenses = []
            
            for expense_data in expense_events_data:
                expense_id = expense_data.get('id')
                is_deleted = expense_data.get('is_deleted', False)
                
                if expense_id:
                    # Update existing expense
                    try:
                        expense = Event.objects.get(id=expense_id, contract=contract)
                        previous_expense_data = expense.to_dict()
                        
                        if is_deleted:
                            # Soft delete the expense
                            expense.is_deleted = True
                            expense.updated_by = request.user
                            expense.save(update_fields=['is_deleted', 'updated_by'])
                            expense.create_history_record(previous_expense_data, request.user)
                            
                            # Delete old notifications for this expense
                            delete_notifications_by_category("expense", str(expense.id))
                            
                            deleted_expenses.append(str(expense.id))
                        else:
                            # Update expense
                            from expenses.serializers.expenses import EventCreateSerializer
                            expense_serializer = EventCreateSerializer(
                                expense,
                                data=expense_data,
                                partial=True
                            )
                            if expense_serializer.is_valid():
                                updated_expense = expense_serializer.save(updated_by=request.user)
                                updated_expense.create_history_record(previous_expense_data, request.user)
                                updated_expenses.append(str(updated_expense.id))
                            else:
                                return Response(
                                    {"error": f"Invalid expense data for ID {expense_id}", "details": expense_serializer.errors},
                                    status=http_status.HTTP_400_BAD_REQUEST
                                )
                    except Event.DoesNotExist:
                        return Response(
                            {"error": f"Expense with ID {expense_id} not found or not associated with this contract"},
                            status=http_status.HTTP_404_NOT_FOUND
                        )
                else:
                    # Create new expense if not marked as deleted
                    if not is_deleted:
                        from expenses.serializers.expenses import EventCreateSerializer
                        # Ensure the expense is linked to this contract
                        expense_data['contract_id'] = str(contract.id)
                        
                        expense_serializer = EventCreateSerializer(data=expense_data)
                        if expense_serializer.is_valid():
                            new_expense = expense_serializer.save(
                                created_by=request.user,
                                updated_by=request.user
                            )
                            # Link to contract
                            new_expense.contract = contract
                            new_expense.save()
                            created_expenses.append(str(new_expense.id))
                        else:
                            return Response(
                                {"error": "Invalid new expense data", "details": expense_serializer.errors},
                                status=http_status.HTTP_400_BAD_REQUEST
                            )
            
            # Create history record with the previous state
            contract.create_history_record(previous_data, request.user)
            
            # Delete old notifications for the contract before creating new ones
            delete_notifications_by_category("contract", str(contract.id))
            
            # Create notification for contract update
            
            # Determine what was changed
            changes = []
            current_data = contract.to_dict()
            
            # Check key fields for changes
            if previous_data.get('title') != current_data.get('title'):
                changes.append(f"title changed from '{previous_data.get('title')}' to '{current_data.get('title')}'")
            
            if previous_data.get('status') != current_data.get('status'):
                changes.append(f"status changed from '{previous_data.get('status')}' to '{current_data.get('status')}'")
            
            if previous_data.get('start_date') != current_data.get('start_date') or previous_data.get('end_date') != current_data.get('end_date'):
                changes.append("contract dates changed")
                
            if previous_data.get('total_amount') != current_data.get('total_amount'):
                changes.append(f"amount changed from {previous_data.get('total_amount')} to {current_data.get('total_amount')}")
            
            # Add document changes to description
            if created_documents or updated_documents or deleted_documents:
                doc_changes = []
                if created_documents:
                    doc_changes.append(f"{len(created_documents)} document(s) added")
                if updated_documents:
                    doc_changes.append(f"{len(updated_documents)} document(s) updated")
                if deleted_documents:
                    doc_changes.append(f"{len(deleted_documents)} document(s) deleted")
                changes.append(f"documents: {', '.join(doc_changes)}")
            
            # Add event changes to description
            if created_incomes or updated_incomes or deleted_incomes:
                event_changes = []
                if created_incomes:
                    event_changes.append(f"{len(created_incomes)} income(s) created")
                if updated_incomes:
                    event_changes.append(f"{len(updated_incomes)} income(s) updated")
                if deleted_incomes:
                    event_changes.append(f"{len(deleted_incomes)} income(s) deleted")
                changes.append(f"income events: {', '.join(event_changes)}")
            
            if created_expenses or updated_expenses or deleted_expenses:
                event_changes = []
                if created_expenses:
                    event_changes.append(f"{len(created_expenses)} expense(s) created")
                if updated_expenses:
                    event_changes.append(f"{len(updated_expenses)} expense(s) updated")
                if deleted_expenses:
                    event_changes.append(f"{len(deleted_expenses)} expense(s) deleted")
                changes.append(f"expense events: {', '.join(event_changes)}")
            
            # Add renewal terms changes
            if renewal_terms_updated:
                changes.append("renewal terms updated")
            
            change_description = ", ".join(changes) if changes else "details updated"
            
            create_system_notification(
                title=f"Contract '{contract.title}' Updated",
                title_ar=f"تم تحديث العقد '{contract.title}'",
                message=f"The contract '{contract.title}' was updated by {request.user.email}: {change_description}",
                message_ar=f"تم تحديث العقد '{contract.title}' بواسطة {request.user.email}: {change_description}",
                priority="low",
                notification_type_name="Contract Update",
                category="contract",
                category_id=str(contract.id)
            )
            
            # Return detailed contract info with update summary
            detail_serializer = ContractDetailSerializer(contract)
            response_data = detail_serializer.data
            
            # Add update summary to response
            response_data['update_summary'] = {
                'documents': {
                    'created': len(created_documents),
                    'updated': len(updated_documents),
                    'deleted': len(deleted_documents),
                    'created_ids': created_documents,
                    'updated_ids': updated_documents,
                    'deleted_ids': deleted_documents
                },
                'income_events': {
                    'created': len(created_incomes),
                    'updated': len(updated_incomes),
                    'deleted': len(deleted_incomes),
                    'created_ids': created_incomes,
                    'updated_ids': updated_incomes,
                    'deleted_ids': deleted_incomes
                },
                'expense_events': {
                    'created': len(created_expenses),
                    'updated': len(updated_expenses),
                    'deleted': len(deleted_expenses),
                    'created_ids': created_expenses,
                    'updated_ids': updated_expenses,
                    'deleted_ids': deleted_expenses
                },
                'renewal_terms_updated': renewal_terms_updated
            }
            
            return Response(response_data)
    except Contract.DoesNotExist:
        return Response(
            {"error": f"Contract with ID {contract_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return Response(
            {"error": "Failed to update contract", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_contract_history(request, contract_id):
    """Retrieve history records for a specific contract"""
    try:
        # First check if contract exists
        contract = get_object_or_404(Contract, id=contract_id)
        
        # Get all history records for this contract
        history_records = ContractHistory.objects.filter(contract=contract).order_by('-modified_at')
        
        # Handle case of no history records
        if not history_records.exists():
            return Response({
                "contract_id": contract_id,
                "contract_title": contract.title,
                "history": [],
                "message": "No history records found for this contract"
            })
        
        # Serialize the history records
        serializer = ContractHistorySerializer(history_records, many=True)
        
        return Response({
            "contract_id": contract_id,
            "contract_title": contract.title,
            "history": serializer.data,
            "count": history_records.count()
        })
    except Contract.DoesNotExist:
        return Response(
            {"error": f"Contract with ID {contract_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve contract history", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def create_contract_with_expenses(request):
    """Create a contract with associated expense events in a single transaction"""
    try:
        with transaction.atomic():            # Extract data
            contract_data = request.data.get('contract', {})
            expense_events_data = request.data.get('installments', [])
            renewal_terms_data = request.data.get('renewal_terms', {})
            
            # Extract common data for expense events
            priority = request.data.get('priority', 'medium')
            # contact_ids = [request.data.get('contact_id')] if request.data.get('contact_id') else []
            contact_id = request.data.get('contact_id')
            # location_ids = [request.data.get('location_id')] if request.data.get('location_id') else []
            location_id = request.data.get('location_id')
            type_id = request.data.get('type_id')
            status = request.data.get('status', 'upcoming')
            notification_date = request.data.get('notification_view_date')
            print(f"notification_date: {request.data}")
            
            if not contract_data:
                return Response(
                    {"error": "Contract data is required"}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
            
            # Set contact_id and location_id in contract_data from common fields if not provided
            if request.data.get('contact_id') and 'contact_id' not in contract_data:
                contract_data['contact_id'] = request.data.get('contact_id')
                
            if request.data.get('location_id') and 'location_id' not in contract_data:
                contract_data['location_id'] = request.data.get('location_id')
            
            # Add renewal terms to contract data if provided
            if renewal_terms_data:
                contract_data['renewal_terms'] = renewal_terms_data
            
            # Validate that total_amount in contract equals sum of expense amounts
            from decimal import Decimal
            contract_amount = Decimal(str(contract_data.get('total_amount', '0')))
            expenses_total = sum(Decimal(str(expense.get('amount', '0'))) for expense in expense_events_data)
            
            if contract_amount != expenses_total:
                return Response(
                    {"error": "Contract total amount must equal the sum of all expense amounts", 
                     "details": f"Contract: {contract_amount}, Sum of expenses: {expenses_total}"}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
            
            contract_data['documents'] = request.data.get('documentUpload', [])
            
            # Create contract
            serializer = ContractCreateSerializer(data=contract_data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid contract data", "details": serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
                
            # Save contract with current user as creator
            contract = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )
            documents_data = request.data.get('documentUpload', [])
            for doc in documents_data:
                ContractDocument.objects.create(
                    contract=contract,
                    name=doc.get('name', 'Unnamed Document'),
                    type=doc.get('type', 'contract'),
                    file=doc.get('file'),  # If you're using file URLs or uploaded files
                    file_type=doc.get('file_type', 'pdf'),
                    url=doc.get('url')  # Optional if you store on cloud
                )
            
            # Create notification for new contract
            create_system_notification(
                title="New Contract Created",
                title_ar="تم إنشاء عقد جديد",
                message=f"A new contract '{contract.title}' was created by {request.user.email}",
                message_ar=f"تم إنشاء عقد جديد '{contract.title}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Contract Created",
                category="contract",
                category_id=str(contract.id)
            )
            
            # Create notification for contract renewal if applicable
            if contract.renewal_terms and contract.renewal_terms.notice_period_days:
                notice_period_days = contract.renewal_terms.notice_period_days
                
                if notice_period_days and contract.end_date:
                    notification_date = contract.end_date - timedelta(days=notice_period_days)
                    
                    if notification_date > timezone.now().date():
                        create_system_notification(
                            title="Contract Renewal Notification",
                            title_ar="إشعار تجديد العقد",
                            message=f"The contract '{contract.title}' has a renewal notification set for {notification_date.strftime('%Y-%m-%d')}",
                            message_ar=f"تم تعيين إشعار تجديد العقد '{contract.title}' في {notification_date.strftime('%Y-%m-%d')}",
                            priority="low",
                            view_date=notification_date,
                            notification_type_name="Contract Renewal Notification",
                            category="contract",
                            category_id=str(contract.id)
                        )
            
            # Create expense events
            created_expenses = []
            print(f"contact id: {contact_id}")
            print(f"location id: {location_id}")
            
            for index, expense_data in enumerate(expense_events_data, 1):
                # Handle empty string for paid_date
                if expense_data.get('paid_date') == '':
                    expense_data['paid_date'] = None

                # Don't parse any dates here - they should already be properly formatted from frontend
                # Just pass the data as-is to create_contracts_events
                new_expense_data = {
                    "title": f"{contract.title} - {get_ordinal_suffix(index)} Installment",
                    "amount": expense_data.get('amount'),
                    "due_date": expense_data.get('due_date'),
                    "paid_date": expense_data.get('paid_date'),
                    "priority": priority,
                    "contact_id": contact_id,
                    "location_id": location_id,
                    "type_id": type_id,
                    "status": expense_data.get('status', status),
                    "notification_date": expense_data.get('notification_view_date'),
                    "contract_id": str(contract.id),
                    "autoCreate": True,
                }

                # Create the expense event
                new_event = create_contracts_events(new_expense_data, request.user, contract)
                created_expenses.append(new_event)


            
            
            # Return the contract with all created expense events
            response_data = {
                "contract": ContractDetailSerializer(contract).data,
                "expense_events": created_expenses,
                "expense_count": len(created_expenses)
            }
            
            return Response(response_data, status=http_status.HTTP_201_CREATED)
    except Exception as e:
        print(f"Error creating contract with expenses: {str(e)}")
        return Response(
            {"error": "Failed to create contract with expense events", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def get_ordinal_suffix(num):
    """Return the number with ordinal suffix (1st, 2nd, 3rd, etc)"""
    if 10 <= num % 100 <= 20:
        suffix = 'th'
    else:
        suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(num % 10, 'th')
    return f"{num}{suffix}"

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def list_contracts_by_location(request):
    """
    Retrieve all contracts filtered by a specific location ID.
    POST request with location_id in the body.
    """
    try:
        # Get location_id from request body
        location_id = request.data.get('location_id')
        if not location_id:
            return Response(
                {"error": "location_id is required"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )

        # Query for contracts with this location ID
        contracts = Contract.objects.filter(
            location_id=location_id,
            is_deleted=False
        ).order_by('-updated_at')
        
        # Add proper select_related and prefetch_related to optimize queries
        contracts = contracts.select_related(
            'contact',
            'location',
            'created_by',
            'updated_by'
        ).prefetch_related(
            'documents',
            'expense_events',
            Prefetch('contact__types', to_attr='_prefetched_types'),  # Add this line
            Prefetch('location__ownership_shares', 
                     queryset=PartnerShare.objects.select_related('contact'),
                     to_attr='_prefetched_ownership_shares'),
            Prefetch('location__types', to_attr='_prefetched_types')   # Add this line
        )
        
        # Filter by status if provided
        status_param = request.data.get('status')
        if status_param:
            contracts = contracts.filter(status=status_param)
        
        # Check if we have any results
        if not contracts.exists():
            # Get location info for response
            from locations.models import Location
            try:
                location = Location.objects.get(id=location_id)
                location_info = {
                    "id": location_id,
                    "name": location.name,
                    "address": location.address
                }
            except:
                location_info = {"id": location_id}
                
            return Response({
                "location": location_info,
                "contracts": [], 
                "count": 0
            })
        
        # Serialize the results
        serializer = ContractListResponseSerializer(contracts, many=True)
        
        # Get location info for response
        location = contracts.first().location
        location_info = {
            "id": location_id,
            "name": location.name,
            "address": location.address
        }
        
        return Response({
            "location": location_info,
            "contracts": serializer.data, 
            "count": contracts.count()
        }, status=http_status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve contracts by location", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def list_contracts_by_contact(request):
    """
    Retrieve all contracts filtered by a specific contact ID.
    POST request with contact_id in the body.
    """
    try:
        # Get contact_id from request body
        contact_id = request.data.get('contact_id')
        if not contact_id:
            return Response(
                {"error": "contact_id is required"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )

        # Query for contracts with this contact ID
        contracts = Contract.objects.filter(
            contact_id=contact_id,
            is_deleted=False
        ).order_by('-updated_at')
        
        # Add proper select_related and prefetch_related to optimize queries
        contracts = contracts.select_related(
            'contact',
            'location',
            'created_by',
            'updated_by'
        ).prefetch_related(
            'documents',
            'expense_events',
            Prefetch('contact__types', to_attr='_prefetched_types'),  # Add this line
            Prefetch('location__ownership_shares', 
                     queryset=PartnerShare.objects.select_related('contact'),
                     to_attr='_prefetched_ownership_shares'),
            Prefetch('location__types', to_attr='_prefetched_types')   # Add this line
        )
        
        # Filter by status if provided
        status_param = request.data.get('status')
        if status_param:
            contracts = contracts.filter(status=status_param)
        
        # Check if we have any results
        if not contracts.exists():
            # Get contact info for response
            from contacts.models.contacts import Contact
            try:
                contact = Contact.objects.get(id=contact_id)
                contact_info = {
                    "id": contact_id,
                    "name": contact.name,
                    "email": contact.email
                }
            except:
                contact_info = {"id": contact_id}
                
            return Response({
                "contact": contact_info,
                "contracts": [], 
                "count": 0
            })
        
        # Serialize the results
        serializer = ContractListResponseSerializer(contracts, many=True)
        
        # Get contact info for response
        contact = contracts.first().contact
        contact_info = {
            "id": contact_id,
            "name": contact.name,
            "email": contact.email
        }
        
        return Response({
            "contact": contact_info,
            "contracts": serializer.data, 
            "count": contracts.count()
        }, status=http_status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve contracts by contact", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def get_contract_finances(request):
    """
    Retrieve all income and expense events associated with a specific contract.
    
    Expects a POST request with contract_id in the body.
    Returns a structured response with income and expense data.
    """
    try:
        # Get contract_id from request body
        contract_id = request.data.get('contract_id')
        if not contract_id:
            return Response(
                {"error": "contract_id is required"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )
            
        # Verify the contract exists
        try:
            contract = Contract.objects.get(id=contract_id)
        except Contract.DoesNotExist:
            return Response(
                {"error": f"Contract with ID {contract_id} not found"}, 
                status=http_status.HTTP_404_NOT_FOUND
            )
        
        # Get income data using the internal function
        from income.api.income import get_income_by_contract_internal
        income_data = get_income_by_contract_internal(contract_id)
        
        # Get expense data using the internal function
        from expenses.api.expenses import get_expense_by_contract_internal
        expense_data = get_expense_by_contract_internal(contract_id)
        
        # Calculate net amount
        total_income = income_data.get('total', 0)
        total_expenses = expense_data.get('total', 0)
        net_amount = total_income - total_expenses
        
        # Return structured response
        return Response({
            "contract": {
                "id": str(contract.id),
                "title": contract.title,
                "total_amount": float(contract.total_amount)
            },
            "finances": {
                "income": {
                    "events": income_data.get('incomes', []),
                    "count": income_data.get('count', 0),
                    "total": total_income,
                },
                "expenses": {
                    "events": expense_data.get('expenses', []),
                    "count": expense_data.get('count', 0),
                    "total": total_expenses
                },
                "net_amount": float(net_amount)
            }
        }, status=http_status.HTTP_200_OK)
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve financial data for contract", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def soft_delete_contract(request):
    """
    Soft delete a contract and mark associated income and expense events as deleted
    if they are not completed.
    
    Takes contract_id in the request body instead of as a URL parameter.
    Optimized to avoid N+1 query problems.
    """
    try:
        # Get contract_id from request body
        contract_id = request.data.get('contract_id')
        if not contract_id:
            return Response(
                {"error": "contract_id is required in the request body"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )
            
        with transaction.atomic():
            # Get the contract with all needed related data in one query
            contract = get_object_or_404(
                Contract.objects.select_related('location', 'contact'),
                id=contract_id
            )
            
            # Capture previous state for history
            previous_data = contract.to_dict()
            
            # Process associated income events - only non-completed events
            # Prefetch all required data in a single query
            income_events = list(Income.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                contract=contract,
                is_deleted=False
            ).exclude(status='completed'))
            
            # Count for response
            income_count = len(income_events)
            
            # Collect history data before updating
            income_history_records = []
            
            # Prepare batch updates instead of individual saves
            for income in income_events:
                # Store previous state for history
                previous_income_data = income.to_dict()
                
                # Update the description to mention contract deletion
                original_desc = income.description or ""
                income.description = f"{original_desc}\n[This income is marked as deleted because its associated contract '{contract.title}' was deleted.]"
                income.is_deleted = True
                income.updated_by = request.user
                
                # Collect history record data
                income_history_records.append(
                    IncomeHistory(
                        income=income,
                        modified_by=request.user,
                        data=previous_income_data
                    )
                )
            
            # Process completed income events - update description only
            completed_income_events = list(Income.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                contract=contract,
                is_deleted=False,
                status='completed'
            ))
            
            # Collect history data for completed incomes
            completed_income_history_records = []
            
            # Update descriptions for completed incomes
            for income in completed_income_events:
                # Store previous state for history
                previous_income_data = income.to_dict()
                
                # Update the description to mention contract deletion
                original_desc = income.description or ""
                income.description = f"{original_desc}\n[This income's contract '{contract.title}' was deleted.]"
                income.updated_by = request.user
                
                # Collect history record data
                completed_income_history_records.append(
                    IncomeHistory(
                        income=income,
                        modified_by=request.user,
                        data=previous_income_data
                    )
                )
            
            # Process associated expense events - only non-completed events
            # Prefetch all required data in a single query
            expense_events = list(Event.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                contract=contract,
                is_deleted=False
            ).exclude(status='completed'))
            
            # Count for response
            expense_count = len(expense_events)
            
            # Collect history data before updating
            expense_history_records = []
            
            # Prepare batch updates instead of individual saves
            for expense in expense_events:
                # Store previous state for history
                previous_expense_data = expense.to_dict()
                
                # Update the description to mention contract deletion
                original_desc = expense.description or ""
                expense.description = f"{original_desc}\n[This expense is marked as deleted because its associated contract '{contract.title}' was deleted.]"
                expense.is_deleted = True
                expense.updated_by = request.user
                
                # Collect history record data
                expense_history_records.append(
                    EventHistory(
                        event=expense,
                        modified_by=request.user,
                        data=previous_expense_data
                    )
                )
            
            # Process completed expense events - update description only
            completed_expense_events = list(Event.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                contract=contract,
                is_deleted=False,
                status='completed'
            ))
            
            # Collect history data for completed expenses
            completed_expense_history_records = []
            
            # Update descriptions for completed expenses
            for expense in completed_expense_events:
                # Store previous state for history
                previous_expense_data = expense.to_dict()
                
                # Update the description to mention contract deletion
                original_desc = expense.description or ""
                expense.description = f"{original_desc}\n[This expense's contract '{contract.title}' was deleted.]"
                expense.updated_by = request.user
                
                # Collect history record data
                completed_expense_history_records.append(
                    EventHistory(
                        event=expense,
                        modified_by=request.user,
                        data=previous_expense_data
                    )
                )
            
            # Perform bulk updates if there are records to update
            if income_events:
                # Using Django's bulk_update to reduce queries
                Income.objects.bulk_update(
                    income_events, 
                    ['description', 'is_deleted', 'updated_by']
                )
                
                # Create history records in bulk
                IncomeHistory.objects.bulk_create(income_history_records)
            
            if completed_income_events:
                # Using Django's bulk_update to reduce queries (description only)
                Income.objects.bulk_update(
                    completed_income_events, 
                    ['description', 'updated_by']
                )
                
                # Create history records in bulk
                IncomeHistory.objects.bulk_create(completed_income_history_records)
            
            if expense_events:
                # Using Django's bulk_update to reduce queries
                Event.objects.bulk_update(
                    expense_events, 
                    ['description', 'is_deleted', 'updated_by']
                )
                
                # Create history records in bulk
                EventHistory.objects.bulk_create(expense_history_records)
                
            if completed_expense_events:
                # Using Django's bulk_update to reduce queries (description only)
                Event.objects.bulk_update(
                    completed_expense_events, 
                    ['description', 'updated_by']
                )
                
                # Create history records in bulk
                EventHistory.objects.bulk_create(completed_expense_history_records)
            
            # Finally, mark the contract as deleted
            contract.is_deleted = True
            contract.updated_by = request.user
            contract.save(update_fields=['is_deleted', 'updated_by'])
            
            # Create history record for contract
            ContractHistory.objects.create(
                contract=contract,
                modified_by=request.user,
                data=previous_data
            )
            
            # Delete old notifications for this contract before creating new ones
            delete_notifications_by_category("contract", str(contract.id))
            
            # Create notification for contract deletion
            create_system_notification(
                title=f"Contract '{contract.title}' Deleted",
                title_ar=f"تم حذف العقد '{contract.title}'",
                message=f"The contract '{contract.title}' was deleted by {request.user.email}, along with {income_count} income events and {expense_count} expense events.",
                message_ar=f"تم حذف العقد '{contract.title}' بواسطة {request.user.email}، مع {income_count} دخل و {expense_count} مصروف.",
                priority="medium",
                notification_type_name="Contract Deleted",
                category="contract",
                category_id=str(contract.id)
            )
            
            # Return success response with details
            return Response({
                "message": f"Contract '{contract.title}' has been deleted successfully",
                "details": {
                    "contract_id": str(contract.id),
                    "associated_income_deleted": income_count,
                    "associated_expenses_deleted": expense_count
                }
            }, status=http_status.HTTP_200_OK)
            
    except Contract.DoesNotExist:
        return Response(
            {"error": f"Contract with ID {contract_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to delete contract", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

