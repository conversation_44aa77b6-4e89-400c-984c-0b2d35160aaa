from rest_framework import serializers
from django.utils import timezone
from income.models.incomeEvents import Income, IncomeHistory, IncomeType
from contacts.serializers.contacts import ContactSimpleSerializer
from locations.serializers.locations import LocationMinimalSerializer , LocationSimpleSerializer
from users.serializers.users import UserDetailSerializer
from contacts.models.contacts import Contact
from locations.models.locations import Location
from reservations.serializers.reservations import ReservationSimpleSerializer
from reservations.models.reservations import Reservation


class IncomeTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeType
        fields = ['id', 'name', 'created_at']
        read_only_fields = ['id', 'created_at']

class IncomeHistorySerializer(serializers.ModelSerializer):
    modified_by = UserDetailSerializer(read_only=True)
    
    class Meta:
        model = IncomeHistory
        fields = ['id', 'modified_at', 'modified_by', 'data']
        read_only_fields = fields

class IncomeCreateSerializer(serializers.ModelSerializer):
    contact_id = serializers.UUIDField(required=False, allow_null=True, write_only=True)
    location_id = serializers.UUIDField(required=False, allow_null=True, write_only=True)
    type_id = serializers.UUIDField(required=False, allow_null=True)
    parent_id = serializers.UUIDField(required=False, allow_null=True)
    reservation_id = serializers.UUIDField(required=False, allow_null=True)
    expense_id = serializers.UUIDField(required=False, allow_null=True, write_only=True)
    contract_id = serializers.UUIDField(required=False, allow_null=True, write_only=True)
    
    class Meta:
        model = Income
        fields = [
            'title', 'amount', 'due_date', 'received_date', 'expense_id',
            'description', 'status', 'priority',
            'type_id', 'contact_id', 'location_id',
            'is_deleted', 'parent_id', 'reservation_id', 'contract_id'
        ]
        extra_kwargs = {
            'title': {'required': True},
            'amount': {'required': True},
            'due_date': {'required': True}
        }

    def validate(self, data):
        # Validate and assign contact
        contact_id = data.pop('contact_id', None)
        if contact_id:
            try:
                contact = Contact.objects.get(pk=contact_id)
                if contact.is_deleted:
                    raise serializers.ValidationError({"contact_id": f"Contact with ID {contact_id} has been deleted"})
                data['contact'] = contact
            except Contact.DoesNotExist:
                raise serializers.ValidationError({"contact_id": f"Contact with ID {contact_id} does not exist"})

        # Validate location
        location_id = data.pop('location_id', None)
        if location_id:
            try:
                location = Location.objects.get(pk=location_id)
                if location.is_deleted:
                    raise serializers.ValidationError({"location_id": f"Location with ID {location_id} has been deleted"})
                data['location'] = location
            except Location.DoesNotExist:
                raise serializers.ValidationError({"location_id": f"Location with ID {location_id} does not exist"})

        # Validate and assign type
        type_id = data.pop('type_id', None)
        if type_id:
            try:
                data['type'] = IncomeType.objects.get(pk=type_id)
            except IncomeType.DoesNotExist:
                raise serializers.ValidationError({"type_id": "Income type does not exist"})

        # Validate and assign parent
        parent_id = data.pop('parent_id', None)
        if parent_id:
            from income.models.parentEvents import ParentIncome
            try:
                data['parent'] = ParentIncome.objects.get(pk=parent_id)
            except ParentIncome.DoesNotExist:
                raise serializers.ValidationError({"parent_id": "Parent income does not exist"})

        # Validate and assign reservation
        reservation_id = data.pop('reservation_id', None)
        if reservation_id:
            try:
                data['reservation'] = Reservation.objects.get(pk=reservation_id)
            except Reservation.DoesNotExist:
                raise serializers.ValidationError({"reservation_id": "Reservation does not exist"})
        
        # Validate expense ID
        expense_id = data.pop('expense_id', None)
        if expense_id:
            from expenses.models.expensesEvents import Event
            try:
                data['expense'] = Event.objects.get(pk=expense_id)
            except Event.DoesNotExist:
                raise serializers.ValidationError({"expense_id": "Expense does not exist"})
            
        # Validate received date
        if 'received_date' in data and data.get('received_date') is not None:
            if 'status' not in data:
                data['status'] = Income.IncomeStatus.COMPLETED
            elif data.get('status') == Income.IncomeStatus.CANCELLED:
                raise serializers.ValidationError({"received_date": "Cannot set received date for a cancelled income"})

        #validate contract ID
        contract_id = data.pop('contract_id', None)
        if contract_id:
            from contracts.models.contracts import Contract
            try:
                data['contract'] = Contract.objects.get(pk=contract_id)
            except Contract.DoesNotExist:
                raise serializers.ValidationError({"contract_id": "Contract does not exist"})
        
        return data
class IncomeSimpleSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    type = serializers.SerializerMethodField()
    contact = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    lastEdited = serializers.SerializerMethodField()
    lastEditedBy = serializers.SerializerMethodField()
    category = serializers.CharField(default="income", read_only=True)
    dueDate = serializers.SerializerMethodField()
    receivedDate = serializers.SerializerMethodField()
    actual_amount = serializers.SerializerMethodField()
    expense_id = serializers.UUIDField(source='expense.id', read_only=True)

    class Meta:
        model = Income
        fields = [
            'id', 'title', 'amount', 'actual_amount', 'dueDate', 'receivedDate',
            'status', 'status_display', 'priority', 'priority_display',
            'type', 'is_deleted', 'contact', 'location',
            'lastEdited', 'lastEditedBy', 'category', 'reservation_id', 'created_at',
            'expense_id' 
        ]
        read_only_fields = fields

    def get_type(self, obj):
        return obj.type.name if obj.type else ""

    def get_dueDate(self, obj):
        return obj.due_date.date() if obj.due_date else None

    def get_receivedDate(self, obj):
        return obj.received_date.date() if obj.received_date else None

    def get_contact(self, obj):
        contact = obj.contact
        if contact:
            return {
                "id": contact.id,
                "name": contact.name,
                "email": contact.email
            }
        return None

    def get_location(self, obj):
        location = obj.location
        if location:
            # Use cached values if they exist
            if hasattr(location, '_ownership_processed'):
                return {
                    "id": location.id,
                    "name": location.name,
                    "address": location.address,
                    "areWeOwner": location._has_primary_owner
                }
            return {
                "id": location.id,
                "name": location.name,
                "address": location.address,
                "areWeOwner": location.are_we_owners()
            }
        return None
    
    def get_actual_amount(self, obj):
        if obj.location:
            # Use cached percentage if it exists (avoids database queries)
            if hasattr(obj.location, '_ownership_processed'):
                if not obj.location._has_primary_owner:
                    from decimal import Decimal
                    return str(obj.amount * Decimal(obj.location._our_percentage / 100))
            # Fallback to method calls (will still use prefetched data)
            elif not obj.location.are_we_owners():
                from decimal import Decimal
                return str(obj.amount * Decimal(obj.location.get_our_Percentage() / 100))
        return obj.amount

    def get_lastEdited(self, obj):
        if hasattr(obj, 'prefetched_history') and obj.prefetched_history:
            return obj.prefetched_history[0]['modified_at']
        elif self.context.get('prefetched_data'):
            return None
        history = obj.history.last()
        return history.modified_at.strftime("%Y-%m-%d %H:%M:%S") if history else None

    def get_lastEditedBy(self, obj):
        if hasattr(obj, 'prefetched_history') and obj.prefetched_history:
            return obj.prefetched_history[0]['modified_by']
        elif self.context.get('prefetched_data'):
            return None
        history = obj.history.last()
        return history.modified_by.username if history and history.modified_by else None

class IncomeDetailSerializer(serializers.ModelSerializer):
    contact = ContactSimpleSerializer(read_only=True)
    location = LocationMinimalSerializer(read_only=True)
    created_by = UserDetailSerializer(read_only=True)
    updated_by = UserDetailSerializer(read_only=True)
    history = IncomeHistorySerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    type = IncomeTypeSerializer(read_only=True)
    category = serializers.SerializerMethodField()
    reservation = ReservationSimpleSerializer(read_only=True)
    parent_title = serializers.CharField(source='parent.title', read_only=True)
    parent_id = serializers.UUIDField(source='parent.id', read_only=True)
    actual_amount = serializers.SerializerMethodField()
    
    class Meta:
        model = Income
        fields = [
            'id', 'category', 'title', 'amount', 'actual_amount' ,'due_date', 'received_date',
            'description', 'status', 'status_display', 
            'priority', 'priority_display', 'type', 'contact', 'location',
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'is_deleted', 'history', 'reservation', 'parent_id', 'parent_title'
        ]
        read_only_fields = fields
    
    def get_category(self, obj):
        return "income"
    
    
    def get_actual_amount(self, obj):
        if obj.location and not obj.location.are_we_owners():
            from decimal import Decimal
            return str(obj.amount * Decimal(obj.location.get_our_Percentage()))
        return obj.amount
