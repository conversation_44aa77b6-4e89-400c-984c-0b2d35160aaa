from django.urls import path
from contacts.api.contacts import (
    create_contact,
    list_contacts,
    get_contact,
    update_contact,
    permanent_delete_contact,
    get_contact_history,
    list_contact_types,
    create_contact_type,
    delete_contact_type,
    soft_delete_contact,
    get_contact_finances
    
)

urlpatterns = [
    # Existing contact endpoints
    path('getall/', list_contacts, name='list_contacts'),
    path('create/', create_contact, name='create_contact'),
    path('getcontact/<uuid:contact_id>/', get_contact, name='get_contact'),
    path('updatecontact/<uuid:contact_id>/', update_contact, name='update_contact'),
    path('permanentdelete/<uuid:contact_id>/', permanent_delete_contact, name='permanent_delete_contact'),
    path('contacts/softdelete/<uuid:contact_id>/', soft_delete_contact, name='soft_delete_contact'),
    path('softdelete/<uuid:contact_id>/', soft_delete_contact, name='soft_delete_contact'),
    path('history/<uuid:contact_id>/', get_contact_history, name='get_contact_history'),
    path('types/', list_contact_types, name='list_contact_types'),
    path('types/create/', create_contact_type, name='create_contact_type'),
    path('types/delete/<uuid:type_id>/', delete_contact_type, name='delete_contact_type'),
    path('contact-finances/' , get_contact_finances, name='get_contact_finances'),   
]
