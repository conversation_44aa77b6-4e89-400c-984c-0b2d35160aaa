from django.urls import path
from contracts.api.contracts import (
    create_contract,
    list_contracts,
    get_contract,
    update_contract,
    get_contract_history,
    create_contract_with_expenses,
    list_contracts_by_location,
    list_contracts_by_contact,
    get_contract_finances,
    soft_delete_contract
)

urlpatterns = [
    path('getall/', list_contracts, name='list_contracts'),
    path('create/', create_contract, name='create_contract'),
    path('getcontract/<uuid:contract_id>/', get_contract, name='get_contract'),
    path('updatecontract/<uuid:contract_id>/', update_contract, name='update_contract'),
    path('soft-delete/', soft_delete_contract, name='soft_delete_contract'),
    path('history/<uuid:contract_id>/', get_contract_history, name='get_contract_history'),
    path('create-with-expenses/', create_contract_with_expenses, name='create_contract_with_expenses'),
    path('by-location/', list_contracts_by_location, name='contracts_by_location'),
    path('by-contact/', list_contracts_by_contact, name='contracts_by_contact'),
    path('contract-finances/', get_contract_finances, name='get_contract_finances'),
]

