from rest_framework import serializers
from notifications.models.notifications import Notification, NotificationType
from users.serializers.users import UserDetailSerializer

class NotificationTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationType
        fields = ['id', 'name', 'created_at']
        read_only_fields = ['id', 'created_at']

class NotificationCreateSerializer(serializers.ModelSerializer):
    type_id = serializers.UUIDField(required=False, allow_null=True)
    
    class Meta:
        model = Notification
        fields = ['title', 'title_ar', 'message', 'message_ar', 'priority', 'type_id', 'category', 'category_id']
        extra_kwargs = {
            'title': {'required': True},
            'message': {'required': True},
        }
        
    def validate(self, data):
        # Validate type exists if provided
        type_id = data.pop('type_id', None)
        if type_id:
            try:
                notification_type = NotificationType.objects.get(pk=type_id)
                data['type'] = notification_type
            except NotificationType.DoesNotExist:
                raise serializers.ValidationError({"type_id": "Notification type does not exist"})
        
        return data

class NotificationDetailSerializer(serializers.ModelSerializer):
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    # Replace the full serializer with a customized field to use prefetched data
    read_by = serializers.SerializerMethodField()
    is_read = serializers.SerializerMethodField()
    type = NotificationTypeSerializer(read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            'id', 'title', 'title_ar', 'message', 'message_ar', 'priority', 'priority_display', 
            'type', 'read_by', 'created_at', 'is_read', 'view_date', 'category', 'category_id'
        ]
        read_only_fields = fields
    
    def get_read_by(self, obj):
        """Return minimal user data from prefetched read_by_list if available"""
        if hasattr(obj, 'read_by_list'):
            return [{'email': user.email} for user in obj.read_by_list]
        # Fallback if prefetched data isn't available
        return [{'email': user.email} for user in obj.read_by.all()]
    
    def get_is_read(self, obj):
        """Check if the request user has read this notification using prefetched data"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            # Use prefetched data if available to avoid an additional query
            if hasattr(obj, 'read_by_list'):
                return any(user.id == request.user.id for user in obj.read_by_list)
            # Fallback to regular query if prefetched data is not available
            return request.user in obj.read_by.all()
        return False
