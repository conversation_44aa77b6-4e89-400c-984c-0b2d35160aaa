import uuid
from django.db import models
from django.utils import timezone
from contacts.models.contacts import Contact
from locations.models.locations import Location
from users.models.users import User
from reservations.models.reservations import Reservation
from contracts.models.contracts import Contract
class IncomeType(models.Model):
    """Model for income event types"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_income_types'
    )
    
    def __str__(self):
        return self.name
    
    class Meta:
        ordering = ['name']

class Income(models.Model):
    """Model for income events"""

    class IncomeStatus(models.TextChoices):
        COMPLETED = 'completed', 'Completed'
        PENDING = 'pending', 'Pending'
        CANCELLED = 'cancelled', 'Cancelled'
        UPCOMING = 'upcoming', 'Upcoming'
        OVERDUE = 'overdue', 'Overdue'

    class IncomePriority(models.TextChoices):
        LOW = 'low', 'Low'
        MEDIUM = 'medium', 'Medium'
        HIGH = 'high', 'High'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    due_date = models.DateTimeField()
    received_date = models.DateTimeField(null=True, blank=True)
    description = models.TextField(blank=True, null=True)

    status = models.CharField(
        max_length=20,
        choices=IncomeStatus.choices,
        default=IncomeStatus.PENDING
    )
    priority = models.CharField(
        max_length=10,
        choices=IncomePriority.choices,
        default=IncomePriority.MEDIUM
    )

    type = models.ForeignKey(
        IncomeType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='incomes'
    )

    parent = models.ForeignKey(
        'income.ParentIncome',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='child_incomes'
    )

    # New foreign key relationships
    contact = models.ForeignKey(Contact, on_delete=models.SET_NULL, null=True, blank=True, related_name='income_events')
    location = models.ForeignKey(Location, on_delete=models.SET_NULL, null=True, blank=True, related_name='income_events_locations')

    expense = models.ForeignKey(
        'expenses.Event',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='income_events'
    )

    reservation = models.ForeignKey(
        Reservation,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='income_events'
    )

    contract = models.ForeignKey(
        Contract,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        default=None,
        related_name='income_events'
    )
    

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_incomes'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='updated_incomes'
    )

    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.title} - {self.amount} ({self.get_status_display()})"

    def save(self, *args, **kwargs):
        if self.status not in ['completed', 'cancelled'] and self.due_date:
            now = timezone.now()
            if self.due_date < now:
                self.status = self.IncomeStatus.OVERDUE
            else:
                self.status = self.IncomeStatus.UPCOMING

        if self.received_date and self.status != self.IncomeStatus.CANCELLED:
            self.status = self.IncomeStatus.COMPLETED

        super().save(*args, **kwargs)

    def create_history_record(self, data, modified_by):
        if modified_by is not None:
            IncomeHistory.objects.create(
                income=self,
                modified_by=modified_by,
                data=data
            )

    def soft_delete(self, user):
        previous_data = self.to_dict()
        self.is_deleted = True
        super().save(update_fields=['is_deleted'])
        self.create_history_record(previous_data, user)
        return True

    def to_dict(self):
        result = {
            'title': self.title,
            'amount': float(self.amount),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'received_date': self.received_date.isoformat() if self.received_date else None,
            'description': self.description,
            'status': self.status,
            'priority': self.priority,
            'contact': {
                'id': str(self.contact.id),
                'name': self.contact.name
            } if self.contact else None,
            'location': {
                'id': str(self.location.id),
                'name': self.location.name
            } if self.location else None,
        }

        if self.type:
            result['type_id'] = str(self.type.id)
            result['type_name'] = self.type.name
        else:
            result['type_id'] = None
            result['type_name'] = None

        if self.parent:
            result['parent_id'] = str(self.parent.id)
            result['parent_title'] = self.parent.title
        else:
            result['parent_id'] = None
            result['parent_title'] = None

        return result

    class Meta:
        ordering = ['-due_date']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['priority']),
            models.Index(fields=['is_deleted']),
        ]


class IncomeHistory(models.Model):
    """Model to track income event change history"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    income = models.ForeignKey(
        Income,
        on_delete=models.CASCADE,
        related_name='history'
    )
    modified_at = models.DateTimeField(auto_now_add=True)
    modified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='income_modifications'
    )
    data = models.JSONField()
    
    class Meta:
        ordering = ['-modified_at']
        verbose_name_plural = 'Income histories'
    
    def __str__(self):
        return f"History for {self.income.title} at {self.modified_at}"
