from django.urls import path, include
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

urlpatterns = [
    path('users/', include('users.urls')),  
    path('contacts/', include('contacts.urls')),
    path('locations/', include('locations.urls')),
    path('reservations/', include('reservations.urls')),
    path('contracts/', include('contracts.urls')),
    path('expenses/', include('expenses.urls')),
    path('income/', include('income.urls')),
    path('notifications/', include('notifications.urls')),
]

