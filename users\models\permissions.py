# permissions/modules.py
from django.db import models
from .permissions_base import create_permission_model

BASE_FIELDS = {
    'sidebar': models.BooleanField(default=False),
    'view': models.BooleanField(default=False),
    'create': models.<PERSON>oleanField(default=False),
    'edit': models.BooleanField(default=False),
    'delete': models.BooleanField(default=False),
    'export': models.BooleanField(default=False),
    'import': models.BooleanField(default=False),
    'approve': models.<PERSON>oleanField(default=False),
    'reject': models.BooleanField(default=False),
    'analytics': models.BooleanField(default=False),
    'notifications': models.<PERSON>oleanField(default=False),
    'view_history': models.BooleanField(default=False),
}



LocationsPermissions = create_permission_model('Locations', BASE_FIELDS)

IncomePermissions = create_permission_model('Income', BASE_FIELDS)

ExpensesPermissions = create_permission_model('Expenses', BASE_FIELDS)

CalendarPermissions = create_permission_model('Calendar', BASE_FIELDS)

ReservationsPermissions = create_permission_model('Reservations', BASE_FIELDS)

DashboardPermissions = create_permission_model('Dashboard',BASE_FIELDS)

FinancialsPermissions = create_permission_model('Financials', BASE_FIELDS)

ContactsPermissions = create_permission_model('Contacts', BASE_FIELDS)

SettingsPermissions = create_permission_model('Settings', BASE_FIELDS)

UsersPermissions = create_permission_model(
    'Users',
    {
        **BASE_FIELDS,
        'manage_accounts': models.BooleanField(default=False),
        'view_activity_log': models.BooleanField(default=False)
    }
)

ContractsPermissions = create_permission_model(
    'Contracts',
    {
        **BASE_FIELDS,
        'view_terms': models.BooleanField(default=False),
        'manage_templates': models.BooleanField(default=False)
    }
)


ModulePermissions = {
    'reservations': ReservationsPermissions,
    'dashboard': DashboardPermissions,
    'financials': FinancialsPermissions,
    'contacts': ContactsPermissions,
    'settings': SettingsPermissions,
    'users': UsersPermissions,
    'contracts': ContractsPermissions,
    'locations': LocationsPermissions,
    'income': IncomePermissions,
    'expenses': ExpensesPermissions,
    'calendar': CalendarPermissions
}