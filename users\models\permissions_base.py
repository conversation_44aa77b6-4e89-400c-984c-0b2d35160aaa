from django.db import models
from .users import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db.models.signals import post_save, post_migrate
from django.apps import apps

class BasePermissionModel(models.Model):
    """Abstract base model for all permissions"""
    user_access = models.ForeignKey(
        'UserAccess',
        on_delete=models.CASCADE,
        related_name='%(class)s'
    )
    
    class Meta:
        abstract = True

class ModulePermissions:
    """Container for permission models"""
    modules = {}
    
    @classmethod
    def register(cls, name, model):
        cls.modules[name.lower()] = model
        
    @classmethod
    def create_all_for_user(cls, user_access):
        for model in cls.modules.values():
            model.objects.create(user_access=user_access)

def create_permission_model(name, fields=None):
    """Factory for permission models"""
    if fields is None:
        fields = {}
    
    for field in fields.items():
        fields[field[0]].default = True

    attrs = {
        '__module__': __name__,
        **fields
    }
    
    model = type(f"{name}Permissions", (BasePermissionModel,), attrs)
    ModulePermissions.register(name, model)
    return model

class UserAccess(models.Model):
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='access',
        db_index=True  # Add index for faster lookups
    )
    
    def __getattr__(self, name):
        if name in ModulePermissions.modules:
            model = ModulePermissions.modules[name]
            perm, _ = model.objects.get_or_create(user_access=self)
            return perm
        raise AttributeError(f"No permission set for '{name}'")

@receiver(post_save, sender=User)
def setup_permissions(sender, instance, created, **kwargs):
    if created:
        access = UserAccess.objects.create(user=instance)
        ModulePermissions.create_all_for_user(access)

@receiver(post_save, sender=User)
def create_user_permissions(sender, instance, created, **kwargs):
    """Create permissions for new users"""
    if created:
        # Create UserAccess if it doesn't exist
        UserAccess.objects.get_or_create(user=instance)
        
        for model in ModulePermissions.modules.values():
            model.objects.get_or_create(user_access=instance.access)

@receiver(post_migrate)
def create_permissions_for_existing_users(sender, **kwargs):
    """Create permissions for existing users after migrations"""
    if sender.name == 'users': 
        User = apps.get_model('users', 'User')
        for user in User.objects.filter(access__isnull=True):
            # Create UserAccess if missing
            access, _ = UserAccess.objects.get_or_create(user=user)
            
            # Create all permission records
            for model in ModulePermissions.values():
                model.objects.get_or_create(user_access=access)