import uuid
from django.db import models
from django.utils import timezone
from contacts.models.contacts import Contact
from locations.models.locations import Location
from users.models.users import User

class Reservation(models.Model):
    """Model for tracking location reservations"""
    
    class ReservationStatus(models.TextChoices):
        PENDING = 'pending', 'Pending'
        ACTIVE = 'active', 'Active'
        COMPLETED = 'completed', 'Completed'
        CANCELLED = 'cancelled', 'Cancelled'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    status = models.CharField(
        max_length=20,
        choices=ReservationStatus.choices,
        default=ReservationStatus.PENDING
    )
    
    # Relationships
    contact = models.ForeignKey(
        Contact,
        on_delete=models.PROTECT,
        related_name='reservations'
    )
    location = models.ForeignKey(
        Location,
        on_delete=models.PROTECT,
        related_name='reservations'
    )
    
    # Additional fields
    notes = models.TextField(blank=True, null=True)
    required_capacity = models.PositiveIntegerField()
    
    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_reservations'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='updated_reservations'
    )
    
    # Soft delete field
    is_deleted = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.title} ({self.get_status_display()}) - {self.location.name}"
    
    def save(self, *args, **kwargs):
        """Override save method for custom functionality"""
        # Call the original save method
        super().save(*args, **kwargs)
    
    def create_history_record(self, data, modified_by):
        """Create a history record with specific data"""
        if modified_by is not None:
            ReservationHistory.objects.create(
                reservation=self,
                modified_by=modified_by,
                data=data
            )
    
    def soft_delete(self, user):
        """Mark reservation as deleted without removing from database"""
        # Save previous state before deletion
        previous_data = self.to_dict()
        
        self.is_deleted = True
        self.status = self.ReservationStatus.CANCELLED
        super().save(update_fields=['is_deleted', 'status'])
        
        # Create history record with previous data
        self.create_history_record(previous_data, user)
        return True
    
    def to_dict(self):
        """Convert reservation to dictionary for history tracking"""
        return {
            'title': self.title,
            'description': self.description,
            'total_amount': float(self.total_amount),
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'status': self.status,
            'contact_id': str(self.contact_id),
            'contact_name': self.contact.name,
            'location_id': str(self.location_id),
            'location_name': self.location.name,
            'notes': self.notes,
            'required_capacity': self.required_capacity
        }
    
    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['start_date']),
            models.Index(fields=['end_date']),
            models.Index(fields=['is_deleted']),
        ]


class ReservationHistory(models.Model):
    """Model to track reservation change history"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reservation = models.ForeignKey(
        Reservation,
        on_delete=models.CASCADE,
        related_name='history'
    )
    modified_at = models.DateTimeField(auto_now_add=True)
    modified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='reservation_modifications'
    )
    # Store the reservation data as JSON
    data = models.JSONField()
    
    class Meta:
        ordering = ['-modified_at']
        verbose_name_plural = 'Reservation histories'
    
    def __str__(self):
        return f"History for {self.reservation.title} at {self.modified_at}"
