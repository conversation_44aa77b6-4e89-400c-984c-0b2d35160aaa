from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status as http_status  # Rename to avoid conflict
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.db import transaction
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from decimal import Decimal  # Add the Decimal import

from expenses.models.parentEvents import ParentEvent
from expenses.models.expensesEvents import Event, EventType
from expenses.serializers.parentEvents import (
    ParentEventCreateSerializer,
    ParentEventDetailSerializer,
    ParentEventSimpleSerializer
)
from expenses.serializers.expenses import EventCreateSerializer, EventDetailSerializer
from notifications.utils import create_system_notification
from contacts.models.contacts import Contact
from locations.models.locations import Location

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_parent_event(request):
    try:
        with transaction.atomic():
            serializer = ParentEventCreateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
                
            # Save parent event with current user as creator
            parent_event = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )
            
            # Create notification for the new parent event
            create_system_notification(
                title=f"New Parent Expense Created: {parent_event.title}",
                title_ar=f"تم إنشاء مصروف جديد: {parent_event.title}",
                message=f"A new parent expense '{parent_event.title}' for {parent_event.amount} was created by {request.user.email}",
                message_ar=f"تم إنشاء مصروف جديد '{parent_event.title}' بمبلغ {parent_event.amount} بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Expense Created"
            )
            
            # Return detailed event info
            detail_serializer = ParentEventDetailSerializer(parent_event)
            return Response(detail_serializer.data, status=http_status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": "Failed to create parent event", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_parent_events(request):
    try:
        # Filter out soft-deleted events
        parent_events = ParentEvent.objects.filter(is_deleted=False)
            
        # Filter by type
        type_id = request.query_params.get('type_id')
        if type_id:
            parent_events = parent_events.filter(type_id=type_id)
            
        # Search by title or description
        search = request.query_params.get('search')
        if search:
            parent_events = parent_events.filter(
                Q(title__icontains=search) | 
                Q(description__icontains=search)
            )
            
        # Order by creation date
        parent_events = parent_events.order_by('-created_at')
            
        # Check if we have any results
        if not parent_events.exists():
            return Response({"parent_events": [], "count": 0})
            
        serializer = ParentEventSimpleSerializer(parent_events, many=True)
        return Response({"parent_events": serializer.data, "count": parent_events.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve parent events", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_parent_event(request, parent_id):
    try:
        parent_event = get_object_or_404(ParentEvent, id=parent_id)
        serializer = ParentEventDetailSerializer(parent_event)
        return Response(serializer.data)
    except ParentEvent.DoesNotExist:
        return Response(
            {"error": f"Parent event with ID {parent_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve parent event", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_parent_event(request, parent_id):
    try:
        with transaction.atomic():
            parent_event = get_object_or_404(ParentEvent, id=parent_id)
            
            serializer = ParentEventCreateSerializer(
                parent_event,
                data=request.data,
                partial=True
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
            
            # Update with current user as updater
            parent_event = serializer.save(updated_by=request.user)
            
            # Return detailed parent event info
            detail_serializer = ParentEventDetailSerializer(parent_event)
            return Response(detail_serializer.data)
    except ParentEvent.DoesNotExist:
        return Response(
            {"error": f"Parent event with ID {parent_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to update parent event", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_parent_event(request, parent_id):
    try:
        parent_event = get_object_or_404(ParentEvent, id=parent_id)
        
        # Check if this parent has any child events
        child_count = parent_event.child_events_count()
        
        if (child_count > 0):
            return Response(
                {"error": f"Cannot delete parent event with {child_count} child events. Remove child events first."}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )
        
        # Mark as deleted
        parent_event.is_deleted = True
        parent_event.save()
        
        return Response({
            "message": f"Parent event '{parent_event.title}' has been deleted"
        }, status=http_status.HTTP_200_OK)
    except ParentEvent.DoesNotExist:
        return Response(
            {"error": f"Parent event with ID {parent_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete parent event", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_parent_with_children(request):
    """Create a parent event with multiple child events in a single transaction"""
    try:
        with transaction.atomic():
            # Extract data
            parent_data = request.data.get('parent', {})
            children_data = request.data.get('children', [])
            
            # Extract common data for children
            priority = request.data.get('priority', 'medium')
            contact_ids = request.data.get('contact_ids', [])
            location_ids = request.data.get('location_ids', [])
            type_id = request.data.get('type_id')
            status = request.data.get('status', 'upcoming')  # Added status field with default 'upcoming'
            
            if not parent_data:
                return Response(
                    {"error": "Parent event data is required"}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
            
            # Remove type_id from parent_data if present
            if 'type_id' in parent_data:
                parent_data.pop('type_id')
                
            # Validate that total amount in parent equals sum of child amounts
            parent_amount = Decimal(str(parent_data.get('amount', '0')))
            children_total = sum(Decimal(str(child.get('amount', '0'))) for child in children_data)
            
            if parent_amount != children_total:
                return Response(
                    {"error": "Parent amount must equal the sum of all children amounts", 
                     "details": f"Parent: {parent_amount}, Sum of children: {children_total}"}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
                
            # Create parent event
            parent_serializer = ParentEventCreateSerializer(data=parent_data)
            if not parent_serializer.is_valid():
                return Response(
                    {"error": "Invalid parent event data", "details": parent_serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
                
            # Save parent without type_id, using type_id from common data
            parent_event = None
            if type_id:
                # Try to find the type and attach it to the parent - do it in one query
                try:
                    from expenses.models.expensesEvents import EventType
                    event_type = EventType.objects.get(pk=type_id)
                    parent_event = parent_serializer.save(
                        created_by=request.user,
                        updated_by=request.user,
                        type=event_type
                    )
                except EventType.DoesNotExist:
                    # If type doesn't exist, create parent without type
                    parent_event = parent_serializer.save(
                        created_by=request.user,
                        updated_by=request.user
                    )
            else:
                # Create parent without type
                parent_event = parent_serializer.save(
                    created_by=request.user,
                    updated_by=request.user
                )
            
            # Optimization 1: Pre-fetch needed objects to avoid repeated queries
            contacts = []
            locations = []
            
            if contact_ids:
                contacts = Contact.objects.filter(id__in=contact_ids).prefetch_related('types').all()
                if len(contacts) != len(contact_ids):
                    return Response(
                        {"error": "One or more contacts not found"}, 
                        status=http_status.HTTP_400_BAD_REQUEST
                    )
            
            if location_ids:
                locations = Location.objects.filter(id__in=location_ids).prefetch_related('types').all()
                if len(locations) != len(location_ids):
                    return Response(
                        {"error": "One or more locations not found"}, 
                        status=http_status.HTTP_400_BAD_REQUEST
                    )
                
            # Create child events - optimized to reduce DB queries
            created_children = []
            child_events = []
            
            for index, child_data in enumerate(children_data, 1):
                # Auto-generate title based on parent title and installment number
                title = f"{parent_event.title} {index}{get_ordinal_suffix(index)} installment"
                description = f"{parent_event.description} - Installment {index} of {len(children_data)}"
                
                # Create Event object but don't save to DB yet
                child = Event(
                    title=title,
                    description=description,
                    amount=child_data.get('amount'),
                    due_date=child_data.get('due_date'),
                    priority=priority,
                    type_id=type_id,
                    parent=parent_event,
                    created_by=request.user,
                    updated_by=request.user,
                    status=child_data.get('status', status)  # Use child-specific status if provided, otherwise use common status
                )
                
                child_events.append(child)
            
            # Optimization 2: Bulk create child events
            created_children = Event.objects.bulk_create(child_events)
            
            # Optimization 3: Bulk add contacts and locations to children
            if contacts:
                # Create through-model instances for M2M relationships
                contact_through_models = []
                for child in created_children:
                    for contact in contacts:
                        contact_through_models.append(
                            Event.contacts.through(event_id=child.id, contact_id=contact.id)
                        )
                
                # Bulk create the through-model instances
                if contact_through_models:
                    Event.contacts.through.objects.bulk_create(contact_through_models)
            
            if locations:
                # Create through-model instances for M2M relationships
                location_through_models = []
                for child in created_children:
                    for location in locations:
                        location_through_models.append(
                            Event.locations.through(event_id=child.id, location_id=location.id)
                        )
                
                # Bulk create the through-model instances
                if location_through_models:
                    Event.locations.through.objects.bulk_create(location_through_models)
            
            # Optimization 4: Create a single notification for the parent and batch notifications for children
            from notifications.utils import create_system_notification
            
            # Create parent notification
            create_system_notification(
                title=f"New Parent Expense With {len(created_children)} Children Created",
                title_ar=f"تم إنشاء مصروف جديد مع {len(created_children)} أطفال",
                message=f"A new parent expense '{parent_event.title}' with {len(created_children)} child expenses was created by {request.user.email}",
                message_ar=f"تم إنشاء مصروف جديد '{parent_event.title}' مع {len(created_children)} أطفال بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Expense Created"
            )
            
            # Refresh children from database to get all fields populated
            children_with_details = Event.objects.filter(
                id__in=[child.id for child in created_children]
            ).select_related(
                'type', 'created_by', 'updated_by', 'parent'
            ).prefetch_related(
                'contacts', 'locations'
            ).all()
            
            # Serialize children efficiently using serializer
            serialized_children = EventDetailSerializer(children_with_details, many=True).data
            
            # Create detailed parent serializer
            parent_data = ParentEventDetailSerializer(parent_event).data
            parent_data['child_events'] = serialized_children
            
            response_data = {
                "parent": parent_data,
                "children_count": len(created_children)
            }
            
            return Response(response_data, status=http_status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": "Failed to create parent event with children", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def get_ordinal_suffix(num):
    """Return the ordinal suffix for a number (1st, 2nd, 3rd, etc)"""
    if 10 <= num % 100 <= 20:
        return 'th'
    else:
        return {1: 'st', 2: 'nd', 3: 'rd'}.get(num % 10, 'th')