from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from sarayVera.settings import numOfQueriesWraper
from ..serializers.auth import LoginSerializer, LogoutSerializer, RefreshSerializer 
from ..models.permissions_base import UserAccess
from ..serializers.permissions import UserPermissionsLightSerializer , UserPermissionsSerializer
from django.db.models import Prefetch
from django.core.cache import cache


@api_view(['POST'])
@numOfQueriesWraper
def login(request):
    serializer = LoginSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    user = serializer.validated_data['user']
    refresh = RefreshToken.for_user(user)
    
    # Try to get permissions from cache first for admin users
    cache_key = f'user_permissions_{user.id}'
    user_permissions = {}
    
    if user.role == user.Role.ADMIN:
        # For admin users, we can use cached permissions or default full access
        user_permissions = cache.get(cache_key)
        if not user_permissions:
            # If admin permissions not in cache, generate default admin permissions
            user_permissions = serializer.get_default_admin_permissions()
            # Cache for 1 hour (3600 seconds)
            cache.set(cache_key, user_permissions, 3600)
    else:
        user_access = UserAccess.objects.filter(user=user).first()
        
        if user_access:
            serializer = UserPermissionsSerializer(user_access)
            user_permissions = serializer.data
    
    # Prepare response data including privileges
    response_data = {
        'accessToken': str(refresh.access_token),
        'refreshToken': str(refresh),
        'access_exp': refresh.access_token.lifetime.total_seconds(),
        'refresh_exp': refresh.lifetime.total_seconds(),
        'user': {
            'id': user.id,
            'email': user.email,
            'role': user.role
        },
        'permissions': user_permissions,
    }
    
    return Response(response_data, status=status.HTTP_200_OK)

@api_view(['POST'])
def logout(request):
    serializer = LogoutSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        refresh_token = serializer.validated_data['refresh']
        token = RefreshToken(refresh_token)
        token.blacklist()
        return Response(status=status.HTTP_205_RESET_CONTENT)
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )
    
@api_view(['POST'])
def refresh_token(request):
    serializer = RefreshSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    refresh_token = serializer.validated_data['refreshToken']
    permissions = serializer.validated_data.get('permissions', {})
    try:
        token = RefreshToken(refresh_token)
        access_token = str(token.access_token)

        return Response({
            'accessToken': access_token,
            'permissions': permissions,
            'access_exp': token.access_token.lifetime.total_seconds(),
            }, 
            status=status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_400_BAD_REQUEST
        )