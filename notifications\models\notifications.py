import uuid
from django.db import models
from django.utils.timezone import now
from users.models.users import User

class NotificationType(models.Model):
    """Model for notification types"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_notification_types'
    )
    
    def __str__(self):
        return self.name
    
    class Meta:
        ordering = ['name']

class Notification(models.Model):
    """Model for system notifications"""
    
    class NotificationPriority(models.TextChoices):
        LOW = 'low', 'Low'
        MEDIUM = 'medium', 'Medium'
        HIGH = 'high', 'High'
    
    class NotificationCategory(models.TextChoices):
        INCOME = 'income', 'Income'
        EXPENSE = 'expense', 'Expense'
        RESERVATION = 'reservation', 'Reservation'
        CONTRACT = 'contract', 'Contract'
        LOCATION = 'location', 'Location'
        CONTACT = 'contact', 'Contact'
        USER = 'user', 'User',
        INCOME_DEU_DATE = 'income_due_date', 'Income Due Date'
        EXPENSE_DEU_DATE = 'expense_due_date', 'Expense Due Date'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    title_ar = models.CharField(max_length=255, null=True, blank=True, help_text="Arabic title")
    message = models.TextField()
    message_ar = models.TextField(null=True, blank=True, help_text="Arabic message")
    priority = models.CharField(
        max_length=10,
        choices=NotificationPriority.choices,
        default=NotificationPriority.MEDIUM
    )
    type = models.ForeignKey(
        NotificationType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='notifications'
    )
    category = models.CharField(
        max_length=20,
        choices=NotificationCategory.choices,
        null=True,
        blank=True,
        help_text="Category of the notification"
    )
    category_id = models.UUIDField(
        null=True,
        blank=True,
        help_text="ID of the related object in the specified category"
    )
    read_by = models.ManyToManyField(
        User,
        related_name='read_notifications',
        blank=True
    )
    view_date = models.DateTimeField(null=True, blank=True, default=None)  # New field to track when the notification was viewed
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.title} ({self.get_priority_display()})"
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['priority']),
            models.Index(fields=['created_at']),
            models.Index(fields=['category']),
            models.Index(fields=['category_id']),
        ]
