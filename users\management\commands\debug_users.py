from django.core.management.base import BaseCommand
from django.contrib.auth import authenticate
from users.models.users import User

class Command(BaseCommand):
    help = 'Debug user authentication issues'

    def add_arguments(self, parser):
        parser.add_argument('--email', type=str, help='Email to test')
        parser.add_argument('--password', type=str, help='Password to test')
        parser.add_argument('--fix-users', action='store_true', help='Fix inactive users')

    def handle(self, *args, **options):
        self.stdout.write("=== User Authentication Debug ===")
        
        # List all users
        self.stdout.write("\n--- All Users ---")
        for user in User.objects.all():
            self.stdout.write(
                f"Email: {user.email}, "
                f"Role: {user.role}, "
                f"Active: {user.is_active}, "
                f"Staff: {user.is_staff}, "
                f"Superuser: {user.is_superuser}, "
                f"Has Password: {bool(user.password)}"
            )
        
        # Fix users if requested
        if options['fix_users']:
            self.stdout.write("\n--- Fixing Users ---")
            for user in User.objects.filter(is_active=False):
                user.is_active = True
                user.save()
                self.stdout.write(f"Fixed user: {user.email} - set to active")
        
        # Test specific user if provided
        if options['email'] and options['password']:
            email = options['email']
            password = options['password']
            
            self.stdout.write(f"\n--- Testing Authentication for {email} ---")
            
            # Check if user exists
            try:
                user = User.objects.get(email=email)
                self.stdout.write(f"User found: {user}")
                self.stdout.write(f"User active: {user.is_active}")
                self.stdout.write(f"User staff: {user.is_staff}")
                self.stdout.write(f"User superuser: {user.is_superuser}")
                
                # Test password
                if user.check_password(password):
                    self.stdout.write("✓ Password check passed")
                else:
                    self.stdout.write("✗ Password check failed")
                
                # Test authenticate
                auth_user = authenticate(email=email, password=password)
                if auth_user:
                    self.stdout.write("✓ Authentication passed")
                else:
                    self.stdout.write("✗ Authentication failed")
                    
            except User.DoesNotExist:
                self.stdout.write(f"✗ User {email} does not exist")
        
        self.stdout.write("\n=== Debug Complete ===")
