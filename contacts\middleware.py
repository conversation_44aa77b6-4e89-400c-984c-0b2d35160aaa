class UTF8ResponseMiddleware:
    """
    Middleware to ensure all responses are encoded as UTF-8
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Process the request
        response = self.get_response(request)
        
        # Ensure content type is set with UTF-8 charset
        if 'Content-Type' in response and 'charset' not in response['Content-Type']:
            if 'application/json' in response['Content-Type']:
                response['Content-Type'] = 'application/json; charset=utf-8'
            elif 'text/' in response['Content-Type']:
                response['Content-Type'] = f"{response['Content-Type']}; charset=utf-8"
                
        return response
