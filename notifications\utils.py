from notifications.models.notifications import Notification, NotificationType
from django.utils import timezone

def create_system_notification(title, title_ar, message, message_ar, priority='medium', notification_type_name=None, view_date=None, category=None, category_id=None):
    """
    Create a system notification
    
    Args:
        title (str): The notification title
        message (str): The notification message
        priority (str): low, medium, or high (default: medium)
        notification_type_name (str, optional): If provided, will link to the notification type
        view_date (datetime, optional): If provided, notification will only be visible after this date (default: now)
    
    Returns:
        Notification: The created notification object
    """
    # Set default view_date to now if not provided
    if view_date is None:
        view_date = timezone.now()
    
    # Find or create notification type if specified
    notification_type = None
    if notification_type_name:
        notification_type, _ = NotificationType.objects.get_or_create(
            name=notification_type_name
        )
    
    # Create the notification
    notification = Notification.objects.create(
        title=title,
        title_ar=title_ar,
        message=message,
        message_ar=message_ar,
        priority=priority,
        type=notification_type,
        view_date=view_date,
        category=category,
        category_id=category_id
    )
    
    return notification

def delete_notifications_by_category(category, category_id):
    """
    Delete all notifications with the specified category and category_id
    
    Args:
        category (str): The notification category (income, expense, reservation, contract, location, contact, user)
        category_id (str/UUID): The ID of the related object in the specified category
    
    Returns:
        int: The number of notifications deleted
    """
    if not category or not category_id:
        return 0
    
    # Validate category is one of the allowed choices
    valid_categories = [choice[0] for choice in Notification.NotificationCategory.choices]
    if category not in valid_categories:
        return 0
    
    # Delete notifications matching the category and category_id
    deleted_count, _ = Notification.objects.filter(
        category=category,
        category_id=category_id
    ).delete()
    
    return deleted_count
