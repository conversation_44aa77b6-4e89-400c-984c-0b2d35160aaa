import uuid
from django.db import models
from django.utils import timezone
from contacts.models.contacts import Contact
from locations.models.locations import Location
from users.models.users import User
from django.db import models

class FileUpload(models.Model):
    file = models.FileField(upload_to='uploads/') 
    file_type = models.CharField(max_length=100)
    name = models.CharField(max_length=255)  
    upload_date = models.DateTimeField(auto_now_add=True)
    url = models.URLField(blank=True, null=True)

    def __str__(self):
        return self.name



class RenewalTerms(models.Model):
    """Model for storing contract renewal terms"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    auto_renew = models.BooleanField(default=False)
    increase_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=0,
        help_text="Percentage increase upon renewal"
    )
    notice_period_days = models.PositiveIntegerField(
        default=30,
        help_text="Number of days notice required before renewal/termination"
    )
    
    def __str__(self):
        return f"{'Auto-renew' if self.auto_renew else 'Manual renewal'} with {self.increase_percentage}% increase"


from django.db import models

class ContractDocument(models.Model):
    contract = models.ForeignKey(
        'Contract',
        on_delete=models.CASCADE,
        related_name='documents'
    )
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=100, default='contract')  # optional
    file = models.FileField(upload_to='contracts/pdfs/')
    file_type = models.CharField(max_length=50, default='pdf')  # optional
    uploaded_at = models.DateTimeField(auto_now_add=True)
    url = models.URLField(blank=True, null=True)  # optional if using cloud storage

    def __str__(self):
        return self.name



class Contract(models.Model):
    """Model for client contracts"""
    class ContractStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        EXPIRED = 'expired', 'Expired'
        PENDING = 'pending', 'Pending'
        TERMINATED = 'terminated', 'Terminated'
        DRAFT = 'draft', 'Draft'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    status = models.CharField(
        max_length=20,
        choices=ContractStatus.choices,
        default=ContractStatus.DRAFT
    )
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    
    # Relationships - changed from client to contact
    contact = models.ForeignKey(
        Contact,
        on_delete=models.PROTECT,
        related_name='contracts'
    )
    location = models.ForeignKey(
        Location,
        on_delete=models.PROTECT,
        related_name='contracts'
    )
    renewal_terms = models.OneToOneField(
        RenewalTerms,
        on_delete=models.CASCADE,
        related_name='contract',
        null=True,
        blank=True
    )
    
    # Financial details
    total_amount = models.DecimalField(max_digits=12, decimal_places=2)
    
    # Additional fields
    notes = models.TextField(blank=True, null=True)
    
    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_contracts'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='updated_contracts'
    )
    
    # Soft delete field
    is_deleted = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.title} - {self.get_status_display()}"
    
    def save(self, *args, **kwargs):
        """Override save method for custom functionality"""
        # Call the original save method
        super().save(*args, **kwargs)
    
    def create_history_record(self, data, modified_by):
        """Create a history record with specific data"""
        if modified_by is not None:
            ContractHistory.objects.create(
                contract=self,
                modified_by=modified_by,
                data=data
            )
    
    def soft_delete(self, user):
        """Mark contract as deleted without removing from database"""
        # Save previous state before deletion
        previous_data = self.to_dict()
        
        self.is_deleted = True
        super().save(update_fields=['is_deleted'])
        
        # Create history record with previous data
        self.create_history_record(previous_data, user)
        return True
    
    def to_dict(self):
        """Convert contract to dictionary for history tracking"""
        # Get renewal terms data
        renewal_terms_data = None
        if self.renewal_terms:
            renewal_terms_data = {
                'auto_renew': self.renewal_terms.auto_renew,
                'increase_percentage': float(self.renewal_terms.increase_percentage),
                'notice_period_days': self.renewal_terms.notice_period_days
            }
        
        # Get documents data
        documents_data = [{
            'id': str(doc.id),
            'name': doc.name,
            'type': doc.type,
            'file_type': doc.file_type,
            'url': doc.url
        } for doc in self.documents.all()]
        
        # Get contact name efficiently
        contact_name = self.contact.name if self.contact else None
        
        # Get location name efficiently  
        location_name = self.location.name if self.location else None
        
        return {
            'title': self.title,
            'description': self.description,
            'status': self.status,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'contact_id': str(self.contact_id),
            'contact_name': contact_name,
            'location_id': str(self.location_id),
            'location_name': location_name,
            'total_amount': float(self.total_amount),
            'renewal_terms': renewal_terms_data,
            'documents': documents_data,
            'notes': self.notes
        }
    
    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['start_date']),
            models.Index(fields=['end_date']),
            models.Index(fields=['is_deleted']),
        ]


class ContractHistory(models.Model):
    """Model to track contract change history"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        related_name='history'
    )
    modified_at = models.DateTimeField(auto_now_add=True)
    modified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='contract_modifications'
    )
    # Store the contract data as JSON
    data = models.JSONField()
    
    class Meta:
        ordering = ['-modified_at']
        verbose_name_plural = 'Contract histories'
    
    def __str__(self):
        return f"History for {self.contract.title} at {self.modified_at}"
