from django.urls import path
from income.api.income import (
    create_income,
    get_income,
    update_income,
    delete_income,
    get_income_history,
    mark_income_received,
    list_income_types,
    create_income_type,
    delete_income_type,
    list_all_incomes,
    list_incomes_by_location,
    list_incomes_by_contact,
    soft_delete_income,
    create_multiple_incomes,
    update_income_status  # Add this import
)
from income.api.parentEvents import (
    create_parent_income,
    list_parent_incomes,
    get_parent_income,
    update_parent_income,
    delete_parent_income,
    create_parent_with_children
    
)

urlpatterns = [
    # Existing income endpoints
    path('create/', create_income, name='create_income'),
    path('createMultiple/', create_multiple_incomes, name='create_multiple_incomes'),
    path('getincome/<uuid:income_id>/', get_income, name='get_income'),
    path('updateincome/<uuid:income_id>/', update_income, name='update_income'),
    path('updatestatus/', update_income_status, name='update_income_status'),  # Remove income_id parameter
    path('deleteincome/<uuid:income_id>/', delete_income, name='delete_income'),
    
    # Fix: Change 'softdelete/<uuid:income_id>/' to match the URL format in the error message
    path('income/softdelete/<uuid:income_id>/', soft_delete_income, name='soft_delete_income'),
    # Also provide the original path for backward compatibility
    path('softdelete/<uuid:income_id>/', soft_delete_income, name='soft_delete_income_alt'),
    path('history/<uuid:income_id>/', get_income_history, name='get_income_history'),
    path('markincomereceived/<uuid:income_id>/', mark_income_received, name='mark_income_received'),
    path('listall/', list_all_incomes, name='list_all_incomes'),

    # New endpoints for income types
    path('types/', list_income_types, name='list_income_types'),
    path('types/create/', create_income_type, name='create_income_type'),
    path('types/delete/<uuid:type_id>/', delete_income_type, name='delete_income_type'),
    
    # New endpoints for parent incomes
    path('parent/create/', create_parent_income, name='create_parent_income'),
    path('parent/getall/', list_parent_incomes, name='list_parent_incomes'),
    path('parent/get/<uuid:parent_id>/', get_parent_income, name='get_parent_income'),
    path('parent/update/<uuid:parent_id>/', update_parent_income, name='update_parent_income'),
    path('parent/delete/<uuid:parent_id>/', delete_parent_income, name='delete_parent_income'),
    path('parent/create-with-children/', create_parent_with_children, name='create_parent_with_children'),

    # New endpoint for listing incomes by location
    path('by-location/', list_incomes_by_location, name='income_by_location'),

    # New endpoint for listing incomes by contact
    path('by-contact/', list_incomes_by_contact, name='income_by_contact'),

    # Fix: Add a URL pattern that exactly matches what the frontend is requesting
    # This will handle requests to /api/softdelete/[uuid]/
    path('softdelete/<str:income_id>/', soft_delete_income, name='soft_delete_income_str'),
]
