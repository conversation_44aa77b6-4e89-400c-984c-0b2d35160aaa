from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status as http_status  # Rename to avoid conflicts
from django.shortcuts import get_object_or_404
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.db import transaction
from django.utils import timezone
from django.db.models import Sum
from django.urls import reverse
from django.conf import settings
from rest_framework.test import APIClient
from django.db.models import Prefetch
from contacts.models import OurCompany
from reservations.models.reservations import Reservation, ReservationHistory
from locations.models.locations import Location
from reservations.serializers.reservations import (
    ReservationCreateSerializer,
    ReservationDetailSerializer,
    ReservationSimpleSerializer,
    ReservationHistorySerializer
)

from income.models.incomeEvents import Income, IncomeHistory
from expenses.models.expensesEvents import Event, EventHistory
from income.serializers.income import IncomeCreateSerializer, IncomeDetailSerializer
from expenses.serializers.expenses import EventSimpleSerializer
from sarayVera.settings import numOfQueriesWraper
from income.api.income import get_income_by_reservation_internal
from expenses.api.expenses import get_expense_by_reservation_internal
from notifications.utils import create_system_notification, delete_notifications_by_category
from expenses.serializers.expenses import EventCreateSerializer
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from django.utils import timezone as django_timezone

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def create_reservation(request):
    try:
        with transaction.atomic():
            # Add request method to context for validation
            serializer = ReservationCreateSerializer(
                data=request.data,
                context={'request_method': 'POST'}
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
                
            # Check capacity conflicts
            location_id = serializer.validated_data['location_id']
            start_date = serializer.validated_data['start_date']
            end_date = serializer.validated_data['end_date']
            required_capacity = serializer.validated_data['required_capacity']

            # Fetch total capacity of the location
            location = Location.objects.get(id=location_id)
            location_total_capacity = location.capacity  # Make sure this field exists

            # Calculate the sum of required_capacity for overlapping reservations
            overlapping_reservations = Reservation.objects.filter(
                location_id=location_id,
                is_deleted=False,
                status__in=['pending', 'active'],
                start_date__lt=end_date,
                end_date__gt=start_date
            )

            used_capacity = overlapping_reservations.aggregate(
                total=Sum('required_capacity')
            )['total'] or 0

            if used_capacity + required_capacity > location_total_capacity:
                return Response(
                    {
                        "error": "Insufficient capacity",
                        "details": f"Only {location_total_capacity - used_capacity} capacity units available for the selected time."
                    },
                    status=http_status.HTTP_400_BAD_REQUEST
                )

            # Save reservation with current user as creator
            reservation = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )
            
            # Mark location as reserved
            location = Location.objects.get(id=location_id)
            # location.is_reserved = True
            # location.updated_by = request.user
            # location.save(update_fields=['is_reserved', 'updated_by'])
            
            # Create notification for new reservation
            from notifications.utils import create_system_notification
            create_system_notification(
                title="New Reservation Created",
                title_ar=f"تم إنشاء حجز جديد: {reservation.title}",
                message=f"A new reservation '{reservation.title}' for location '{location.name}' was created by {request.user.email}",
                message_ar=f"تم إنشاء حجز جديد '{reservation.title}' للموقع '{location.name}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Reservation Created",
                category="reservation",
                category_id=str(reservation.id)  # Use reservation ID as category ID
            )
            
            # Return detailed reservation info
            detail_serializer = ReservationDetailSerializer(reservation)
            return Response(detail_serializer.data, status=http_status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": "Failed to create reservation", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_reservations(request):
    try:
        # Filter out soft-deleted reservations by default
        include_deleted = request.query_params.get('include_deleted', 'false').lower() == 'true'
        
        if include_deleted:
            reservations = Reservation.objects.all().order_by('-updated_at')
        else:
            reservations = Reservation.objects.filter(is_deleted=False).order_by('-updated_at')
        
        # Enhanced select_related and prefetch_related to optimize queries
        reservations = reservations.select_related(
            'contact',
            'location',
            'created_by',
            'updated_by'
        ).prefetch_related(
            'income_events',
            'location__ownership_shares',
            'location__ownership_shares__contact',
            Prefetch('contact__types', to_attr='_prefetched_types'),  # Add this line
            Prefetch('location__types', to_attr='_prefetched_types')   # Add this line
        )
        
        # Apply filters
        # Filter by status
        status_param = request.query_params.get('status')
        if status_param:
            reservations = reservations.filter(status=status_param)
            
        # Filter by date range
        start_from = request.query_params.get('start_from')
        if start_from:
            reservations = reservations.filter(start_date__gte=start_from)
            
        end_before = request.query_params.get('end_before')
        if end_before:
            reservations = reservations.filter(end_date__lte=end_before)
            
        # Filter by contact or location
        contact_id = request.query_params.get('contact_id')
        if contact_id:
            reservations = reservations.filter(contact_id=contact_id)
            
        location_id = request.query_params.get('location_id')
        if location_id:
            reservations = reservations.filter(location_id=location_id)
            
        # Search by title or description
        search = request.query_params.get('search')
        if search:
            reservations = reservations.filter(
                Q(title__icontains=search) | 
                Q(description__icontains=search)
            )
            
        # Check if we have any results
        if not reservations.exists():
            return Response({"reservations": [], "count": 0})
        
        # Pre-calculate capacity information for all locations
        # Get all location IDs from our reservations
        location_ids = set(str(r.location_id) for r in reservations)
        
        # Make a SINGLE query for all active reservations across all relevant locations
        # This replaces the multiple individual queries we were making before
        all_active_reservations = Reservation.objects.filter(
            location_id__in=location_ids,
            is_deleted=False,
            status__in=['pending', 'active']
        ).values('id', 'location_id', 'start_date', 'end_date', 'required_capacity')
        
        # Organize active reservations by location_id
        capacity_data = {}
        for location_id in location_ids:
            # Filter in Python memory instead of making separate DB queries
            location_reservations = [
                res for res in all_active_reservations
                if str(res['location_id']) == location_id
            ]
            capacity_data[location_id] = {
                'reservations': location_reservations
            }
        
        # Use serializer with our optimized capacity data
        serializer = ReservationSimpleSerializer(
            reservations, 
            many=True,
            context={'capacity_data': capacity_data}
        )
        
        return Response({"reservations": serializer.data, "count": reservations.count()})
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve reservations", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_reservation(request, reservation_id):
    try:
        reservation = get_object_or_404(Reservation, id=reservation_id)
        serializer = ReservationDetailSerializer(reservation)
        return Response(serializer.data)
    except Reservation.DoesNotExist:
        return Response(
            {"error": f"Reservation with ID {reservation_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve reservation", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_reservation(request, reservation_id):
    try:
        with transaction.atomic():
            reservation = get_object_or_404(Reservation, id=reservation_id)
            
            # Capture the previous state before making changes
            previous_data = reservation.to_dict()
            
            # Extract events data from request
            income_events_data = request.data.get('income_events', [])
            expense_events_data = request.data.get('expense_events', [])
            
            # Remove events data from reservation data to avoid validation errors
            reservation_data = request.data.copy()
            reservation_data.pop('income_events', None)
            reservation_data.pop('expense_events', None)
            
            # Add request method to context for validation
            serializer = ReservationCreateSerializer(
                reservation,
                data=reservation_data,
                partial=True,
                context={'request_method': 'PATCH'}
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=http_status.HTTP_400_BAD_REQUEST
                )
                
            # Check for scheduling conflicts if dates are being changed
            if 'start_date' in serializer.validated_data or 'end_date' in serializer.validated_data:
                location_id = serializer.validated_data.get('location_id', reservation.location_id)
                start_date = serializer.validated_data.get('start_date', reservation.start_date)
                end_date = serializer.validated_data.get('end_date', reservation.end_date)
                
                conflicting_reservations = Reservation.objects.filter(
                    location_id=location_id,
                    is_deleted=False,
                    status__in=['pending', 'active'],
                    start_date__lt=end_date,
                    end_date__gt=start_date
                ).exclude(id=reservation_id).exclude(status='cancelled')
            
            # Update reservation with current user as updater
            reservation = serializer.save(updated_by=request.user)
            
            # Process income events
            created_incomes = []
            updated_incomes = []
            deleted_incomes = []
            
            for income_data in income_events_data:
                income_id = income_data.get('id')
                is_deleted = income_data.get('is_deleted', False)
                
                if income_id:
                    # Update existing income
                    try:
                        income = Income.objects.get(id=income_id, reservation=reservation)
                        previous_income_data = income.to_dict()
                        
                        if is_deleted:
                            # Soft delete the income
                            income.is_deleted = True
                            income.updated_by = request.user
                            income.save(update_fields=['is_deleted', 'updated_by'])
                            income.create_history_record(previous_income_data, request.user)
                            
                            # Delete old notifications for this income
                            delete_notifications_by_category("income", str(income.id))
                            
                            deleted_incomes.append(str(income.id))
                        else:
                            # Update income
                            from income.serializers.income import IncomeCreateSerializer
                            income_serializer = IncomeCreateSerializer(
                                income,
                                data=income_data,
                                partial=True
                            )
                            if income_serializer.is_valid():
                                updated_income = income_serializer.save(updated_by=request.user)
                                updated_income.create_history_record(previous_income_data, request.user)
                                updated_incomes.append(str(updated_income.id))
                            else:
                                return Response(
                                    {"error": f"Invalid income data for ID {income_id}", "details": income_serializer.errors},
                                    status=http_status.HTTP_400_BAD_REQUEST
                                )
                    except Income.DoesNotExist:
                        return Response(
                            {"error": f"Income with ID {income_id} not found or not associated with this reservation"},
                            status=http_status.HTTP_404_NOT_FOUND
                        )
                else:
                    # Create new income if not marked as deleted
                    if not is_deleted:
                        from income.serializers.income import IncomeCreateSerializer
                        # Ensure the income is linked to this reservation
                        income_data['reservation_id'] = str(reservation.id)
                        
                        income_serializer = IncomeCreateSerializer(data=income_data)
                        if income_serializer.is_valid():
                            new_income = income_serializer.save(
                                created_by=request.user,
                                updated_by=request.user
                            )
                            # Link to reservation
                            new_income.reservation = reservation
                            new_income.save()
                            created_incomes.append(str(new_income.id))
                        else:
                            return Response(
                                {"error": "Invalid new income data", "details": income_serializer.errors},
                                status=http_status.HTTP_400_BAD_REQUEST
                            )
            
            # Process expense events
            created_expenses = []
            updated_expenses = []
            deleted_expenses = []
            
            for expense_data in expense_events_data:
                expense_id = expense_data.get('id')
                is_deleted = expense_data.get('is_deleted', False)
                
                if expense_id:
                    # Update existing expense
                    try:
                        expense = Event.objects.get(id=expense_id, reservation=reservation)
                        previous_expense_data = expense.to_dict()
                        
                        if is_deleted:
                            # Soft delete the expense
                            expense.is_deleted = True
                            expense.updated_by = request.user
                            expense.save(update_fields=['is_deleted', 'updated_by'])
                            expense.create_history_record(previous_expense_data, request.user)
                            
                            # Delete old notifications for this expense
                            delete_notifications_by_category("expense", str(expense.id))
                            
                            deleted_expenses.append(str(expense.id))
                        else:
                            # Update expense
                            from expenses.serializers.expenses import EventCreateSerializer
                            expense_serializer = EventCreateSerializer(
                                expense,
                                data=expense_data,
                                partial=True
                            )
                            if expense_serializer.is_valid():
                                updated_expense = expense_serializer.save(updated_by=request.user)
                                updated_expense.create_history_record(previous_expense_data, request.user)
                                updated_expenses.append(str(updated_expense.id))
                            else:
                                return Response(
                                    {"error": f"Invalid expense data for ID {expense_id}", "details": expense_serializer.errors},
                                    status=http_status.HTTP_400_BAD_REQUEST
                                )
                    except Event.DoesNotExist:
                        return Response(
                            {"error": f"Expense with ID {expense_id} not found or not associated with this reservation"},
                            status=http_status.HTTP_404_NOT_FOUND
                        )
                else:
                    # Create new expense if not marked as deleted
                    if not is_deleted:
                        from expenses.serializers.expenses import EventCreateSerializer
                        # Ensure the expense is linked to this reservation
                        expense_data['reservation_id'] = str(reservation.id)
                        
                        expense_serializer = EventCreateSerializer(data=expense_data)
                        if expense_serializer.is_valid():
                            new_expense = expense_serializer.save(
                                created_by=request.user,
                                updated_by=request.user
                            )
                            # Link to reservation
                            new_expense.reservation = reservation
                            new_expense.save()
                            created_expenses.append(str(new_expense.id))
                        else:
                            return Response(
                                {"error": "Invalid new expense data", "details": expense_serializer.errors},
                                status=http_status.HTTP_400_BAD_REQUEST
                            )
            
            # Create history record for reservation
            reservation.create_history_record(previous_data, request.user)
            
            # Delete old notifications for the reservation before creating new ones
            delete_notifications_by_category("reservation", str(reservation.id))
            
            # Create notification for reservation update
            from notifications.utils import create_system_notification
            
            # Determine what was changed
            changes = []
            current_data = reservation.to_dict()
            
            # Check key fields for changes
            if previous_data.get('title') != current_data.get('title'):
                changes.append(f"title changed from '{previous_data.get('title')}' to '{current_data.get('title')}'")
            
            if previous_data.get('status') != current_data.get('status'):
                changes.append(f"status changed from '{previous_data.get('status')}' to '{current_data.get('status')}'")
            
            if previous_data.get('start_date') != current_data.get('start_date') or previous_data.get('end_date') != current_data.get('end_date'):
                changes.append("reservation dates changed")
                
            if previous_data.get('total_amount') != current_data.get('total_amount'):
                changes.append(f"amount changed from {previous_data.get('total_amount')} to {current_data.get('total_amount')}")
                
            if previous_data.get('location_name') != current_data.get('location_name'):
                changes.append(f"location changed from '{previous_data.get('location_name')}' to '{current_data.get('location_name')}'")
            
            # Add event changes to description
            if created_incomes or updated_incomes or deleted_incomes:
                event_changes = []
                if created_incomes:
                    event_changes.append(f"{len(created_incomes)} income(s) created")
                if updated_incomes:
                    event_changes.append(f"{len(updated_incomes)} income(s) updated")
                if deleted_incomes:
                    event_changes.append(f"{len(deleted_incomes)} income(s) deleted")
                changes.append(f"income events: {', '.join(event_changes)}")
            
            if created_expenses or updated_expenses or deleted_expenses:
                event_changes = []
                if created_expenses:
                    event_changes.append(f"{len(created_expenses)} expense(s) created")
                if updated_expenses:
                    event_changes.append(f"{len(updated_expenses)} expense(s) updated")
                if deleted_expenses:
                    event_changes.append(f"{len(deleted_expenses)} expense(s) deleted")
                changes.append(f"expense events: {', '.join(event_changes)}")
            
            change_description = ", ".join(changes) if changes else "details updated"
            
            create_system_notification(
                title=f"Reservation '{reservation.title}' Updated",
                title_ar=f"تم تحديث الحجز '{reservation.title}'",
                message=f"The reservation '{reservation.title}' was updated by {request.user.email}: {change_description}",
                message_ar=f"تم تحديث الحجز '{reservation.title}' بواسطة {request.user.email}: {change_description}",
                priority="low",
                notification_type_name="Reservation Update",
                category="reservation",
                category_id=str(reservation.id),
            )
            
            # Handle status changes and location reservation status
            if 'status' in serializer.validated_data:
                new_status = serializer.validated_data['status']
                
                # If reservation is cancelled or completed, update location reserved status
                if new_status in ['cancelled', 'completed']:
                    # Check if there are other active reservations for this location
                    other_active = Reservation.objects.filter(
                        location=reservation.location,
                        is_deleted=False,
                        status__in=['pending', 'active']
                    ).exclude(id=reservation_id).exists()
                    
                    # If no other active reservations, mark location as not reserved
                    if not other_active:
                        location = reservation.location
                        location.is_reserved = False
                        location.updated_by = request.user
                        location.save(update_fields=['is_reserved', 'updated_by'])
            
            # Return detailed reservation info with event update summary
            detail_serializer = ReservationDetailSerializer(reservation)
            response_data = detail_serializer.data
            
            # Add event update summary to response
            response_data['event_updates'] = {
                'income_events': {
                    'created': len(created_incomes),
                    'updated': len(updated_incomes),
                    'deleted': len(deleted_incomes),
                    'created_ids': created_incomes,
                    'updated_ids': updated_incomes,
                    'deleted_ids': deleted_incomes
                },
                'expense_events': {
                    'created': len(created_expenses),
                    'updated': len(updated_expenses),
                    'deleted': len(deleted_expenses),
                    'created_ids': created_expenses,
                    'updated_ids': updated_expenses,
                    'deleted_ids': deleted_expenses
                }
            }
            
            return Response(response_data)
    except Reservation.DoesNotExist:
        return Response(
            {"error": f"Reservation with ID {reservation_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return Response(
            {"error": "Failed to update reservation", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def get_reservations_by_location_and_date(request):
    """
    Retrieve all reservations for a specific location within a date range.
    """
    try:
        # Extract input parameters
        location_id = request.data.get('location_id')
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')

        if not location_id or not start_date or not end_date:
            return Response(
                {"error": "location_id, start_date, and end_date are required"},
                status=http_status.HTTP_400_BAD_REQUEST
            )

        # Validate location
        location = get_object_or_404(Location, id=location_id)

        # Query reservations within the date range
        reservations = Reservation.objects.filter(
            location=location,
            start_date__lt=end_date,
            end_date__gt=start_date,
            is_deleted=False
        ).order_by('start_date')

        # Serialize the reservations
        serializer = ReservationSimpleSerializer(reservations, many=True)

        # Calculate total, taken, and available capacity
        total_capacity = location.capacity
        taken_capacity = sum(res['required_capacity'] for res in serializer.data)
        available_capacity = total_capacity - taken_capacity

        # Return the response
        return Response({
            "location": {
                "id": str(location.id),
                "name": location.name,
                "address": location.address,
                "totalCapacity": total_capacity,
                "takenCapacity": taken_capacity,
                "availableCapacity": available_capacity
            },
            "reservations": serializer.data,
            "count": reservations.count()
        }, status=http_status.HTTP_200_OK)
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve reservations", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_reservation_history(request, reservation_id):
    """Retrieve history records for a specific reservation"""
    try:
        # First check if reservation exists
        reservation = get_object_or_404(Reservation, id=reservation_id)
        
        # Get all history records for this reservation
        history_records = ReservationHistory.objects.filter(reservation=reservation).order_by('-modified_at')
        
        # Handle case of no history records
        if not history_records.exists():
            return Response({
                "reservation_id": reservation_id,
                "reservation_title": reservation.title,
                "history": [],
                "message": "No history records found for this reservation"
            })
        
        # Serialize the history records
        serializer = ReservationHistorySerializer(history_records, many=True)
        
        return Response({
            "reservation_id": reservation_id,
            "reservation_title": reservation.title,
            "history": serializer.data,
            "count": history_records.count()
        })
    except Reservation.DoesNotExist:
        return Response(
            {"error": f"Reservation with ID {reservation_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve reservation history", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

from django.db.models import Sum
from decimal import Decimal
from django.utils.dateparse import parse_datetime

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def create_reservation_with_income(request):
    """Create a reservation with associated income events in a single transaction"""
    try:
        with transaction.atomic():
            # Extract and validate reservation data
            reservation_data = request.data.get('reservation', request.data)
            income_events_data = request.data.get('income_events', [])

            if not reservation_data:
                return Response(
                    {"error": "Reservation data is required"},
                    status=http_status.HTTP_400_BAD_REQUEST
                )

            # Inject common fields into reservation_data if not already present
            for key in ['contact_id', 'location_id']:
                if key in request.data and key not in reservation_data:
                    reservation_data[key] = request.data[key]

            serializer = ReservationCreateSerializer(
                data=reservation_data,
                context={'request_method': 'POST'}
            )

            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid reservation data", "details": serializer.errors},
                    status=http_status.HTTP_400_BAD_REQUEST
                )

            # Capacity conflict check - fix timezone handling
            location_id = reservation_data['location_id']
            start_date = serializer.validated_data['start_date']  # Use validated data instead
            end_date = serializer.validated_data['end_date']      # Use validated data instead
            required_capacity = reservation_data['required_capacity']
            
            # Ensure required_capacity is an integer
            required_capacity = int(reservation_data.get('required_capacity', 0))

            # Ensure dates are timezone-aware if they aren't already
            if start_date and timezone.is_naive(start_date):
                start_date = timezone.make_aware(start_date)
            if end_date and timezone.is_naive(end_date):
                end_date = timezone.make_aware(end_date)

            location = Location.objects.get(id=location_id)
            overlapping_reservations = Reservation.objects.filter(
                location_id=location_id,
                is_deleted=False,
                status__in=['pending', 'active'],
                start_date__lt=end_date,
                end_date__gt=start_date
            )

            used_capacity = overlapping_reservations.aggregate(
                total=Sum('required_capacity')
            )['total'] or 0

            if used_capacity + required_capacity > location.capacity:
                return Response(
                    {
                        "error": "Insufficient capacity",
                        "details": f"Only {location.capacity - used_capacity} capacity units available for the selected time."
                    },
                    status=http_status.HTTP_400_BAD_REQUEST
                )

            # Validate total income == reservation total
            reservation_amount = Decimal(str(reservation_data.get('total_amount', '0')))
            income_total = sum(Decimal(str(i.get('amount', '0'))) for i in income_events_data)
            if reservation_amount != income_total:
                return Response(
                    {
                        "error": "Reservation total amount must equal the sum of all income amounts",
                        "details": f"Reservation: {reservation_amount}, Sum of incomes: {income_total}"
                    },
                    status=http_status.HTTP_400_BAD_REQUEST
                )

            # Save reservation
            reservation = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )

            # Update location reserved status
            location.is_reserved = True
            location.updated_by = request.user
            location.save(update_fields=['is_reserved', 'updated_by'])

            # Create system notification for reservation
            from notifications.utils import create_system_notification
            create_system_notification(
                title="New Reservation Created",
                title_ar=f"تم إنشاء حجز جديد: {reservation.title}",
                message=f"A new reservation '{reservation.title}' for location '{location.name}' was created by {request.user.email}",
                message_ar=f"تم إنشاء حجز جديد '{reservation.title}' للموقع '{location.name}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Reservation Created",
                category="reservation",
                category_id=str(reservation.id)
            )

            # Create income events
            created_incomes = []
            priority = request.data.get('priority', 'medium')
            status = request.data.get('status', 'upcoming')
            type_id = request.data.get('type_id')

            for index, income_data in enumerate(income_events_data, 1):
                # Parse due_date if it's a string
                if 'due_date' in income_data and isinstance(income_data['due_date'], str):
                    try:
                        income_data['due_date'] = parse_datetime(income_data['due_date'])
                        if income_data['due_date'] is None:
                            # Fallback to date parsing if datetime parsing fails
                            from django.utils.dateparse import parse_date
                            date_obj = parse_date(income_data['due_date'])
                            if date_obj:
                                income_data['due_date'] = timezone.make_aware(
                                    timezone.datetime.combine(date_obj, timezone.datetime.min.time())
                                )
                    except Exception as e:
                        print(f"Error parsing due_date: {e}")

                # Parse received_date if it's a string (same logic as due_date)
                if 'received_date' in income_data and isinstance(income_data['received_date'], str):
                    try:
                        income_data['received_date'] = parse_datetime(income_data['received_date'])
                        if income_data['received_date'] is None:
                            # Fallback to date parsing if datetime parsing fails
                            from django.utils.dateparse import parse_date
                            date_obj = parse_date(income_data['received_date'])
                            if date_obj:
                                income_data['received_date'] = timezone.make_aware(
                                    timezone.datetime.combine(date_obj, timezone.datetime.min.time())
                                )
                    except Exception as e:
                        print(f"Error parsing received_date: {e}")

                complete_income_data = {
                    'title': f"{reservation.title} {index}{get_ordinal_suffix(index)} payment",
                    'description': f"{reservation.description or reservation.title} - Payment {index} of {len(income_events_data)}",
                    'priority': priority,
                    'contact_id': str(reservation.contact_id),
                    'location_id': str(reservation.location_id),
                    'type_id': type_id,
                    'status': income_data.get('status', status),
                    'reservation_id': str(reservation.id),
                    **income_data
                }

                income_serializer = IncomeCreateSerializer(data=complete_income_data)
                if income_serializer.is_valid():
                    income = income_serializer.save(
                        created_by=request.user,
                        updated_by=request.user
                    )

                    income.reservation = reservation
                    income.save()

                    if location.are_we_owners():
                        our_id = OurCompany.get_instance().id
                        for share in location.ownership_shares.all():
                            if share.contact.id == our_id:
                                continue
                            percentage = float(share.percentage)
                            income_amount = round((percentage / 100.0) * float(income.amount), 2)
                            expense_data = {
                                "title": f"{income.title} - Share of {share.contact.name}",
                                "amount": income_amount,
                                "due_date": income.due_date,
                                "description": f"Auto-created income for {share.contact.name} from income {income.id}",
                                "status": "upcoming",
                                "income_id": str(income.id),
                                "priority": "medium",
                                "contact_id": str(share.contact.id),
                                "location_id": str(location.id),
                                "reservation_id": complete_income_data.get("reservation_id"),
                                "parent_id": complete_income_data.get("parent_id"),
                                "contract_id": complete_income_data.get("contract_id")
                            }

                            expense_serializer = EventCreateSerializer(data=expense_data)
                            if expense_serializer.is_valid():
                                expense_serializer.save(created_by=request.user, updated_by=request.user)
                                create_system_notification(
                                    title=f"New Expense Created for Income: {income.title}",
                                    title_ar=f"تم إنشاء مصروف جديد للدخل: {income.title}",
                                    message=f"A new expense '{expense_data['title']}' for income '{income.title}' was created for {share.contact.name} with amount {expense_data['amount']}",
                                    message_ar=f"تم إنشاء مصروف جديد '{expense_data['title']}' للدخل '{income.title}' بمبلغ {expense_data['amount']} لـ {share.contact.name}",
                                    priority="low",
                                    notification_type_name="Expense Created",
                                    category="expense",
                                    category_id=str(expense_serializer.instance.id)
                                )
                            else:
                                raise Exception({
                                    "auto_created_expense_error": expense_serializer.errors,
                                    "contact": share.contact.id
                                })

                    created_incomes.append(IncomeDetailSerializer(income).data)
                    create_system_notification(
                        title=f"New Income Created for Reservation: {income.title}",
                        title_ar=f"تم إنشاء دخل جديد للحجز: {income.title}",
                        message=f"A new income '{income.title}' for {income.amount} was created for reservation '{reservation.title}'",
                        message_ar=f"تم إنشاء دخل جديد '{income.title}' بمبلغ {income.amount} للحجز '{reservation.title}' بواسطة {request.user.email}",
                        priority="low",
                        notification_type_name="Income Created",
                        category="income",
                        category_id=str(income.id)
                    )
                    
                    # Handle notification_view_date - improved error handling
                    notification_view_date = income_data.get('notification_view_date')
                    if notification_view_date:
                        try:
                            # Parse notification date if it's a string
                            if isinstance(notification_view_date, str):
                                notification_date = parse_datetime(notification_view_date)
                            else:
                                notification_date = notification_view_date
                                
                            if notification_date:
                                # Ensure both dates are timezone-aware for proper comparison
                                due_date = income.due_date
                                
                                # If notification_date is naive, make it timezone-aware
                                if hasattr(notification_date, 'tzinfo') and notification_date.tzinfo is None:
                                    notification_date = django_timezone.make_aware(notification_date)
                                
                                # If due_date is naive, make it timezone-aware
                                if hasattr(due_date, 'tzinfo') and due_date.tzinfo is None:
                                    due_date = django_timezone.make_aware(due_date)
                                
                                # Convert both to the same timezone for comparison
                                notification_date = django_timezone.localtime(notification_date)
                                due_date = django_timezone.localtime(due_date)
                                
                                # Now perform the comparison safely
                                if notification_date < due_date:
                                    days_difference = (due_date.date() - notification_date.date()).days
                                    reminder_message = f"You should collect {income.amount} in {days_difference} days." if days_difference > 0 else f"You need to collect {income.amount} today or it will be overdue."
                                    reminder_message_ar = f"يجب عليك جمع {income.amount} خلال {days_difference} يومًا." if days_difference > 0 else f"يجب عليك جمع {income.amount} اليوم أو سيتأخر."
                                else:
                                    reminder_message = f"You need to collect {income.amount} today or it will be overdue."
                                    reminder_message_ar = f"يجب عليك جمع {income.amount} اليوم أو سيتأخر."
                                
                                create_system_notification(
                                    title=f"Upcoming Income Due: {income.title}",
                                    title_ar=f"الدخل المستحق قريبًا: {income.title}",
                                    message=reminder_message,
                                    message_ar=reminder_message_ar,
                                    priority="medium",
                                    notification_type_name="Income Reminder",
                                    view_date=notification_date,
                                    category="income_due_date",
                                    category_id=str(income.id)
                                )
                        except Exception as date_error:
                            print(f"Error processing notification date for income: {str(date_error)}")
                            print(f"notification_view_date type: {type(notification_view_date)}")
                            print(f"notification_view_date value: {notification_view_date}")
                else:
                    print(f"Failed to create income: {income_serializer.errors}")

            return Response({
                "reservation": ReservationDetailSerializer(reservation).data,
                "income_events": created_incomes,
                "income_count": len(created_incomes)
            }, status=http_status.HTTP_201_CREATED)

    except Exception as e:
        print(f"Error creating reservation with income: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return Response({"error": str(e)}, status=http_status.HTTP_500_INTERNAL_SERVER_ERROR)
    
def get_ordinal_suffix(num):
    """Return the ordinal suffix for a number (1st, 2nd, 3rd, etc)"""
    if 10 <= num % 100 <= 20:
        return 'th'
    else:
        return {1: 'st', 2: 'nd', 3: 'rd'}.get(num % 10, 'th')

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def list_reservations_by_location(request):
    """
    Retrieve all reservations filtered by a specific location ID.
    POST request with location_id in the body.
    """
    try:
        # Get location_id from request body
        location_id = request.data.get('location_id')
        if not location_id:
            return Response(
                {"error": "location_id is required"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )

        # Query for reservations with this location ID
        reservations = Reservation.objects.filter(
            location_id=location_id,
            is_deleted=False
        ).order_by('-updated_at')
        
        # Add proper select_related and prefetch_related to optimize queries
        reservations = reservations.select_related(
            'contact',
            'location',
            'created_by',
            'updated_by'
        ).prefetch_related(
            'income_events'
        )
        
        # Filter by status if provided
        status_param = request.data.get('status')
        if status_param:
            reservations = reservations.filter(status=status_param)
        
        # Check if we have any results
        if not reservations.exists():
            # Get location info for response
            from locations.models import Location
            try:
                location = Location.objects.get(id=location_id)
                location_info = {
                    "id": location_id,
                    "name": location.name,
                    "address": location.address
                }
            except:
                location_info = {"id": location_id}
                
            return Response({
                "location": location_info,
                "reservations": [], 
                "count": 0
            })
        
        # Serialize the results
        serializer = ReservationSimpleSerializer(reservations, many=True)
        
        # Get location info for response
        location = reservations.first().location
        location_info = {
            "id": location_id,
            "name": location.name,
            "address": location.address
        }
        
        return Response({
            "location": location_info,
            "reservations": serializer.data, 
            "count": reservations.count()
        }, status=http_status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve reservations by location", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def list_reservations_by_contact(request):
    """
    Retrieve all reservations filtered by a specific contact ID.
    POST request with contact_id in the body.
    """
    try:
        # Get contact_id from request body
        contact_id = request.data.get('contact_id')
        if not contact_id:
            return Response(
                {"error": "contact_id is required"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )

        # Query for reservations with this contact ID
        reservations = Reservation.objects.filter(
            contact_id=contact_id,
            is_deleted=False
        ).order_by('-updated_at')
        
        # Add proper select_related and prefetch_related to optimize queries
        reservations = reservations.select_related(
            'contact',
            'location',
            'created_by',
            'updated_by'
        ).prefetch_related(
            'income_events'
        )
        
        # Filter by status if provided
        status_param = request.data.get('status')
        if status_param:
            reservations = reservations.filter(status=status_param)
        
        # Check if we have any results
        if not reservations.exists():
            # Get contact info for response
            from contacts.models.contacts import Contact
            try:
                contact = Contact.objects.get(id=contact_id)
                contact_info = {
                    "id": contact_id,
                    "name": contact.name,
                    "email": contact.email
                }
            except:
                contact_info = {"id": contact_id}
                
            return Response({
                "contact": contact_info,
                "reservations": [], 
                "count": 0
            })
        
        # Serialize the results
        serializer = ReservationSimpleSerializer(reservations, many=True)
        
        # Get contact info for response
        contact = reservations.first().contact
        contact_info = {
            "id": contact_id,
            "name": contact.name,
            "email": contact.email
        }
        
        return Response({
            "contact": contact_info,
            "reservations": serializer.data, 
            "count": reservations.count()
        }, status=http_status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve reservations by contact", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def get_reservation_finances(request):
    """
    Retrieve all income and expense events associated with a specific reservation.
    
    Expects a POST request with reservation_id in the body.
    Returns a structured response with income and expense data.
    """
    try:
        # Get reservation_id from request body
        reservation_id = request.data.get('reservation_id')
        if not reservation_id:
            return Response(
                {"error": "reservation_id is required"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )
            
        # Verify the reservation exists
        try:
            reservation = Reservation.objects.get(id=reservation_id)
        except Reservation.DoesNotExist:
            return Response(
                {"error": f"Reservation with ID {reservation_id} not found"}, 
                status=http_status.HTTP_404_NOT_FOUND
            )
        
        # Get income data using the internal function
        income_data = get_income_by_reservation_internal(reservation_id)
        
        # Get expense data using the internal function
        expense_data = get_expense_by_reservation_internal(reservation_id)
        
        # Calculate net amount
        total_income = income_data.get('total', 0)
        total_expenses = expense_data.get('total', 0)
        net_amount = total_income - total_expenses
        
        # Return structured response
        return Response({
            "reservation": {
                "id": str(reservation.id),
                "title": reservation.title,
                "total_amount": float(reservation.total_amount)
            },
            "finances": {
                "income": {
                    "events": income_data.get('incomes', []),
                    "count": income_data.get('count', 0),
                    "total": total_income
                },
                "expenses": {
                    "events": expense_data.get('expenses', []),
                    "count": expense_data.get('count', 0),
                    "total": total_expenses
                },
                "net_amount": float(net_amount)
            }
        }, status=http_status.HTTP_200_OK)
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve financial data for reservation", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def soft_delete_reservation(request):
    """
    Soft delete a reservation and mark associated income and expense events as deleted
    if they are not completed.
    
    Takes reservation_id in the request body instead of as a URL parameter.
    Optimized to avoid N+1 query problems.
    """
    try:
        # Get reservation_id from request body
        reservation_id = request.data.get('reservation_id')
        if not reservation_id:
            return Response(
                {"error": "reservation_id is required in the request body"}, 
                status=http_status.HTTP_400_BAD_REQUEST
            )
            
        with transaction.atomic():
            # Get the reservation with all needed related data in one query
            reservation = get_object_or_404(
                Reservation.objects.select_related('location', 'contact'),
                id=reservation_id
            )
            
            # Capture previous state for history
            previous_data = reservation.to_dict()
            
            # Process associated income events - only non-completed events
            # Prefetch all required data in a single query
            income_events = list(Income.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                reservation=reservation,
                is_deleted=False
            ).exclude(status='completed'))
            
            # Count for response
            income_count = len(income_events)
            
            # Collect history data before updating
            income_history_records = []
            
            # Prepare batch updates instead of individual saves
            for income in income_events:
                # Store previous state for history
                previous_income_data = income.to_dict()
                
                # Update the description to mention reservation deletion
                original_desc = income.description or ""
                income.description = f"{original_desc}\n[This income is marked as deleted because its associated reservation '{reservation.title}' was deleted.]"
                income.is_deleted = True
                income.updated_by = request.user
                
                # Collect history record data
                income_history_records.append(
                    IncomeHistory(
                        income=income,
                        modified_by=request.user,
                        data=previous_income_data
                    )
                )
            
            # Process completed income events - update description only
            completed_income_events = list(Income.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                reservation=reservation,
                is_deleted=False,
                status='completed'
            ))
            
            # Collect history data for completed incomes
            completed_income_history_records = []
            
            # Update descriptions for completed incomes
            for income in completed_income_events:
                # Store previous state for history
                previous_income_data = income.to_dict()
                
                # Update the description to mention reservation deletion
                original_desc = income.description or ""
                income.description = f"{original_desc}\n[This income's reservation '{reservation.title}' was deleted.]"
                income.updated_by = request.user
                
                # Collect history record data
                completed_income_history_records.append(
                    IncomeHistory(
                        income=income,
                        modified_by=request.user,
                        data=previous_income_data
                    )
                )
            
            # Process associated expense events - only non-completed events
            # Prefetch all required data in a single query
            expense_events = list(Event.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                reservation=reservation,
                is_deleted=False
            ).exclude(status='completed'))
            
            # Count for response
            expense_count = len(expense_events)
            
            # Collect history data before updating
            expense_history_records = []
            
            # Prepare batch updates instead of individual saves
            for expense in expense_events:
                # Store previous state for history
                previous_expense_data = expense.to_dict()
                
                # Update the description to mention reservation deletion
                original_desc = expense.description or ""
                expense.description = f"{original_desc}\n[This expense is marked as deleted because its associated reservation '{reservation.title}' was deleted.]"
                expense.is_deleted = True
                expense.updated_by = request.user
                
                # Collect history record data
                expense_history_records.append(
                    EventHistory(
                        event=expense,
                        modified_by=request.user,
                        data=previous_expense_data
                    )
                )
            
            # Process completed expense events - update description only
            completed_expense_events = list(Event.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                reservation=reservation,
                is_deleted=False,
                status='completed'
            ))
            
            # Collect history data for completed expenses
            completed_expense_history_records = []
            
            # Update descriptions for completed expenses
            for expense in completed_expense_events:
                # Store previous state for history
                previous_expense_data = expense.to_dict()
                
                # Update the description to mention reservation deletion
                original_desc = expense.description or ""
                expense.description = f"{original_desc}\n[This expense's reservation '{reservation.title}' was deleted.]"
                expense.updated_by = request.user
                
                # Collect history record data
                completed_expense_history_records.append(
                    EventHistory(
                        event=expense,
                        modified_by=request.user,
                        data=previous_expense_data
                    )
                )
            
            # Perform bulk updates if there are records to update
            if income_events:
                # Using Django's bulk_update to reduce queries
                Income.objects.bulk_update(
                    income_events, 
                    ['description', 'is_deleted', 'updated_by']
                )
                
                # Create history records in bulk
                IncomeHistory.objects.bulk_create(income_history_records)
            
            if completed_income_events:
                # Using Django's bulk_update to reduce queries (description only)
                Income.objects.bulk_update(
                    completed_income_events, 
                    ['description', 'updated_by']
                )
                
                # Create history records in bulk
                IncomeHistory.objects.bulk_create(completed_income_history_records)
            
            if expense_events:
                # Using Django's bulk_update to reduce queries
                Event.objects.bulk_update(
                    expense_events, 
                    ['description', 'is_deleted', 'updated_by']
                )
                
                # Create history records in bulk
                EventHistory.objects.bulk_create(expense_history_records)
                
            if completed_expense_events:
                # Using Django's bulk_update to reduce queries (description only)
                Event.objects.bulk_update(
                    completed_expense_events, 
                    ['description', 'updated_by']
                )
                
                # Create history records in bulk
                EventHistory.objects.bulk_create(completed_expense_history_records)
            
            # Finally, mark the reservation as deleted
            reservation.is_deleted = True
            reservation.updated_by = request.user
            reservation.save(update_fields=['is_deleted', 'updated_by'])
            
            # Create history record for reservation
            ReservationHistory.objects.create(
                reservation=reservation,
                modified_by=request.user,
                data=previous_data
            )
            
            # Delete old notifications for this reservation before creating new ones
            delete_notifications_by_category("reservation", str(reservation.id))
            
            # Create notification for reservation deletion
            create_system_notification(
                title=f"Reservation '{reservation.title}' Deleted",
                title_ar=f"تم حذف الحجز '{reservation.title}'",
                message=f"The reservation '{reservation.title}' was deleted by {request.user.email}, along with {income_count} income events and {expense_count} expense events.",
                message_ar=f"تم حذف الحجز '{reservation.title}' بواسطة {request.user.email}، مع {income_count} دخل و {expense_count} مصروف.",
                priority="medium",
                notification_type_name="Reservation Deleted",
                category="reservation",
                category_id=str(reservation.id)  # Use reservation ID as category ID
            )
            
            # Return success response with details
            return Response({
                "message": f"Reservation '{reservation.title}' has been deleted successfully",
                "details": {
                    "reservation_id": str(reservation.id),
                    "associated_income_deleted": income_count,
                    "associated_expenses_deleted": expense_count
                }
            }, status=http_status.HTTP_200_OK)
            
    except Reservation.DoesNotExist:
        return Response(
            {"error": f"Reservation with ID {reservation_id} not found"}, 
            status=http_status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to delete reservation", "details": str(e)},
            status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
        )

