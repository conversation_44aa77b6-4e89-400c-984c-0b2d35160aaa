from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.db import transaction
from django.utils import timezone

from income.models.parentEvents import ParentIncome
from income.models.incomeEvents import Income, IncomeType  # Added Income import
from income.serializers.parentEvents import (
    ParentIncomeCreateSerializer,
    ParentEventDetailSerializer,
    ParentIncomeSimpleSerializer,
    
)
from income.serializers.income import IncomeCreateSerializer, IncomeDetailSerializer
from contacts.models.contacts import Contact
from locations.models.locations import Location

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_parent_income(request):
    try:
        with transaction.atomic():
            serializer = ParentIncomeCreateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Save parent income with current user as creator
            parent_income = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )
            
            # Create notification for the new parent income
            from notifications.utils import create_system_notification
            create_system_notification(
                title=f"New Parent Income Created: {parent_income.title}",
                title_ar=f"تم إنشاء دخل جديد: {parent_income.title}",
                message=f"A new parent income '{parent_income.title}' for {parent_income.amount} was created by {request.user.email}",
                message_ar=f"تم إنشاء دخل جديد '{parent_income.title}' بمبلغ {parent_income.amount} بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Income Created"
            )
            
            # Return detailed income info
            detail_serializer = ParentEventDetailSerializer(parent_income)
            return Response(detail_serializer.data, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": "Failed to create parent income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_parent_incomes(request):
    try:
        # Filter out soft-deleted incomes
        parent_incomes = ParentIncome.objects.filter(is_deleted=False)
            
        # Filter by type
        type_id = request.query_params.get('type_id')
        if type_id:
            parent_incomes = parent_incomes.filter(type_id=type_id)
            
        # Search by title or description
        search = request.query_params.get('search')
        if search:
            parent_incomes = parent_incomes.filter(
                Q(title__icontains=search) | 
                Q(description__icontains=search)
            )
            
        # Order by creation date
        parent_incomes = parent_incomes.order_by('-created_at')
            
        # Check if we have any results
        if not parent_incomes.exists():
            return Response({"parent_incomes": [], "count": 0})
            
        serializer = ParentIncomeSimpleSerializer(parent_incomes, many=True)
        return Response({"parent_incomes": serializer.data, "count": parent_incomes.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve parent incomes", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_parent_income(request, parent_id):
    try:
        parent_income = get_object_or_404(ParentIncome, id=parent_id)
        serializer = ParentEventDetailSerializer(parent_income)
        return Response(serializer.data)
    except ParentIncome.DoesNotExist:
        return Response(
            {"error": f"Parent income with ID {parent_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve parent income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_parent_income(request, parent_id):
    try:
        with transaction.atomic():
            parent_income = get_object_or_404(ParentIncome, id=parent_id)
            
            serializer = ParentIncomeCreateSerializer(
                parent_income,
                data=request.data,
                partial=True
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update with current user as updater
            parent_income = serializer.save(updated_by=request.user)
            
            # Return detailed parent income info
            detail_serializer = ParentEventDetailSerializer(parent_income)
            return Response(detail_serializer.data)
    except ParentIncome.DoesNotExist:
        return Response(
            {"error": f"Parent income with ID {parent_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to update parent income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_parent_income(request, parent_id):
    try:
        parent_income = get_object_or_404(ParentIncome, id=parent_id)
        
        # Check if this parent has any child incomes
        child_count = parent_income.child_incomes_count()
        
        if (child_count > 0):
            return Response(
                {"error": f"Cannot delete parent income with {child_count} child incomes. Remove child incomes first."}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Mark as deleted
        parent_income.is_deleted = True
        parent_income.save()
        
        return Response({
            "message": f"Parent income '{parent_income.title}' has been deleted"
        }, status=status.HTTP_200_OK)
    except ParentIncome.DoesNotExist:
        return Response(
            {"error": f"Parent income with ID {parent_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete parent income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_parent_with_children(request):
    """Create a parent income with multiple child incomes in a single transaction"""
    try:
        with transaction.atomic():
            # Extract data
            parent_data = request.data.get('parent', {})
            children_data = request.data.get('children', [])
            
            # Extract common data for children
            priority = request.data.get('priority', 'medium')
            contact_ids = request.data.get('contact_ids', [])
            location_ids = request.data.get('location_ids', [])
            type_id = request.data.get('type_id')
            status = request.data.get('status', 'upcoming')  # Added status field with default 'upcoming'
            
            if not parent_data:
                return Response(
                    {"error": "Parent income data is required"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Remove type_id from parent_data if present
            if 'type_id' in parent_data:
                parent_data.pop('type_id')
                
            # Validate that total amount in parent equals sum of child amounts
            from decimal import Decimal
            parent_amount = Decimal(str(parent_data.get('amount', '0')))
            children_total = sum(Decimal(str(child.get('amount', '0'))) for child in children_data)
            
            if parent_amount != children_total:
                return Response(
                    {"error": "Parent amount must equal the sum of all children amounts", 
                     "details": f"Parent: {parent_amount}, Sum of children: {children_total}"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Create parent income
            parent_serializer = ParentIncomeCreateSerializer(data=parent_data)
            if not parent_serializer.is_valid():
                return Response(
                    {"error": "Invalid parent income data", "details": parent_serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Save parent without type_id, using type_id from common data
            parent_income = None
            if type_id:
                # Try to find the type and attach it to the parent - do it in one query
                try:
                    from income.models.incomeEvents import IncomeType
                    income_type = IncomeType.objects.get(pk=type_id)
                    parent_income = parent_serializer.save(
                        created_by=request.user,
                        updated_by=request.user,
                        type=income_type
                    )
                except IncomeType.DoesNotExist:
                    # If type doesn't exist, create parent without type
                    parent_income = parent_serializer.save(
                        created_by=request.user,
                        updated_by=request.user
                    )
            else:
                # Create parent without type
                parent_income = parent_serializer.save(
                    created_by=request.user,
                    updated_by=request.user
                )
            
            # Optimization 1: Pre-fetch needed objects to avoid repeated queries
            contacts = []
            locations = []
            
            if contact_ids:
                contacts = Contact.objects.filter(id__in=contact_ids).prefetch_related('types').all()
                if len(contacts) != len(contact_ids):
                    return Response(
                        {"error": "One or more contacts not found"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            if location_ids:
                locations = Location.objects.filter(id__in=location_ids).prefetch_related('types').all()
                if len(locations) != len(location_ids):
                    return Response(
                        {"error": "One or more locations not found"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
            # Create child incomes - optimized to reduce DB queries
            created_incomes = []
            child_incomes = []
            
            for index, child_data in enumerate(children_data, 1):
                # Auto-generate title based on parent title and installment number
                title = f"{parent_income.title} {index}{get_ordinal_suffix(index)} installment"
                description = f"{parent_income.description} - Installment {index} of {len(children_data)}"
                
                # Create Income object but don't save to DB yet
                child = Income(
                    title=title,
                    description=description,
                    amount=child_data.get('amount'),
                    due_date=child_data.get('due_date'),
                    priority=priority,
                    type_id=type_id,
                    parent=parent_income,
                    created_by=request.user,
                    updated_by=request.user
                )
                
                child_incomes.append(child)
            
            # Optimization 2: Bulk create child incomes
            created_incomes = Income.objects.bulk_create(child_incomes)
            
            # Optimization 3: Bulk add contacts and locations to children
            if contacts:
                # Create through-model instances for M2M relationships
                contact_through_models = []
                for child in created_incomes:
                    for contact in contacts:
                        contact_through_models.append(
                            Income.contacts.through(income_id=child.id, contact_id=contact.id)
                        )
                
                # Bulk create the through-model instances
                if contact_through_models:
                    Income.contacts.through.objects.bulk_create(contact_through_models)
            
            if locations:
                # Create through-model instances for M2M relationships
                location_through_models = []
                for child in created_incomes:
                    for location in locations:
                        location_through_models.append(
                            Income.locations.through(income_id=child.id, location_id=location.id)
                        )
                
                # Bulk create the through-model instances
                if location_through_models:
                    Income.locations.through.objects.bulk_create(location_through_models)
            
            # Optimization 4: Create a single notification for the parent and batch notifications for children
            from notifications.utils import create_system_notification
            
            # Create parent notification
            create_system_notification(
                title=f"New Parent Income With {len(created_incomes)} Children Created",
                title_ar=f"تم إنشاء دخل جديد مع {len(created_incomes)} أطفال",
                message=f"A new parent income '{parent_income.title}' with {len(created_incomes)} child incomes was created by {request.user.email}",
                message_ar=f"تم إنشاء دخل جديد '{parent_income.title}' مع {len(created_incomes)} أطفال بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Income Created"
            )
            
            # Refresh children from database to get all fields populated
            children_with_details = Income.objects.filter(
                id__in=[child.id for child in created_incomes]
            ).select_related(
                'type', 'created_by', 'updated_by', 'parent'
            ).prefetch_related(
                'contacts', 'locations'
            ).all()
            
            # Serialize children efficiently using serializer
            serialized_children = IncomeDetailSerializer(children_with_details, many=True).data
            
            # Create detailed parent serializer
            parent_data = ParentEventDetailSerializer(parent_income).data
            parent_data['child_incomes'] = serialized_children
            
            response_data = {
                "parent": parent_data,
                "children_count": len(created_incomes)
            }
            
            return Response(response_data, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response(
            {"error": "Failed to create parent income with children", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def get_ordinal_suffix(num):
    """Return the ordinal suffix for a number (1st, 2nd, 3rd, etc)"""
    if 10 <= num % 100 <= 20:
        return 'th'
    else:
        return {1: 'st', 2: 'nd', 3: 'rd'}.get(num % 10, 'th')
