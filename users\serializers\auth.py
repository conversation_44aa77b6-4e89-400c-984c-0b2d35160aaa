from rest_framework import serializers
from django.contrib.auth import authenticate
from django.core.validators import <PERSON>ailValidator
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from ..models.users import User 
from ..models.permissions_base import UserAccess
from ..serializers.permissions import UserPermissionsSerializer, UserPermissionsLightSerializer

class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(
        validators=[EmailValidator()],
        required=True
    )
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )
    
    def get_default_admin_permissions(self):
        """Return default full permissions for admin users"""
        base_module = {
            'sidebar': True,
            'view': True,
            'create': True,
            'edit': True,
            'delete': True,
            'export': True,
            'import': True,
            'approve': True,
            'reject': True,
            'analytics': True,
            'notifications': True,
            'view_history': True,
        }
        
        # Create permissions object with all modules
        permissions = {
            'reservations': base_module.copy(),
            'dashboard': base_module.copy(),
            'financials': base_module.copy(),
            'contacts': base_module.copy(),
            'settings': base_module.copy(),
            'income': base_module.copy(),
            'expenses': base_module.copy(),
            'calendar': base_module.copy(),
            'locations': base_module.copy(),
            'users': {**base_module.copy(), 'manage_accounts': True, 'view_activity_log': True},
            'contracts': {**base_module.copy(), 'view_terms': True, 'manage_templates': True},
        }
        
        return permissions
        
    def to_representation(self, instance):
        """Add privileges to login response"""
        data = super().to_representation(instance)
        user = instance['user']
        
        # Check if the user is admin and add default permissions
        if user.is_superuser:
            data['permissions'] = self.get_default_admin_permissions()
        else:
            # For non-admin users, retrieve permissions as usual
            user_access = UserAccess.objects.filter(user=user).first()
            serializer = UserPermissionsSerializer(user_access)
            data['permissions'] = serializer.data if user_access else {}

        return data

    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')

        if email and password:
            user = authenticate(email=email, password=password)
            if not user:
                raise serializers.ValidationError(
                    "Unable to log in with provided credentials.",
                    code='authorization'
                )
            attrs['user'] = user
        else:
            raise serializers.ValidationError(
                "Must include 'email' and 'password'.",
                code='authorization'
            )

        return attrs

class LogoutSerializer(serializers.Serializer):
    refresh = serializers.CharField(required=True)

    def validate_refresh(self, value):
        try:
            RefreshToken(value)
            return value
        except Exception as e:
            raise serializers.ValidationError("Invalid refresh token")

class RefreshSerializer(serializers.Serializer):
    refreshToken = serializers.CharField()

    def validate(self, attrs):
        try:
            token = RefreshToken(attrs['refreshToken'])
            attrs['accessToken'] = str(token.access_token)

            user = User.objects.get(id=token['user_id'])
            user.last_activity = timezone.now()
            user.save()

            user_access = UserAccess.objects.filter(user=user).first()
            if user_access:
                serializer = UserPermissionsSerializer(user_access)
            attrs['permissions'] = serializer.data if user_access else {}

        except Exception as e:
            raise serializers.ValidationError("Invalid refresh token")
        return attrs

