from rest_framework import serializers
from django.utils import timezone
from reservations.models.reservations import Reservation, ReservationHistory
from contacts.serializers.contacts import ContactSimpleSerializer
from locations.serializers.locations import LocationSimpleSerializer
from users.serializers.users import UserDetailSerializer
from contacts.models.contacts import Contact
from locations.models.locations import Location

class ReservationHistorySerializer(serializers.ModelSerializer):
    modified_by = UserDetailSerializer(read_only=True)
    
    class Meta:
        model = ReservationHistory
        fields = ['id', 'modified_at', 'modified_by', 'data']
        read_only_fields = fields

class ReservationSimpleSerializer(serializers.ModelSerializer):
    location = serializers.SerializerMethodField()
    contact = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    totalCapacity = serializers.SerializerMethodField()
    takenCapacity = serializers.SerializerMethodField()
    availableCapacity = serializers.SerializerMethodField()
    actual_amount = serializers.SerializerMethodField()

    class Meta:
        model = Reservation
        fields = [
            'id', 'title', 'description', 'total_amount', 'start_date', 'end_date', 'actual_amount',
            'status', 'contact', 'location', 'notes', 'required_capacity',
            'status_display', 'is_deleted', 'totalCapacity', 'takenCapacity', 'availableCapacity'
        ]
        read_only_fields = fields

    def get_location(self, obj):
        if not obj.location:
            return None
        return {
            "id": str(obj.location.id),
            "name": obj.location.name,
            "address": obj.location.address,
            "capacity": obj.location.capacity,
            "is_active": obj.location.is_active
        }

    def get_contact(self, obj):
        if not obj.contact:
            return None
        return {
            "id": str(obj.contact.id),
            "name": obj.contact.name,
            "email": obj.contact.email,
            "phone": obj.contact.phone
        }

    def get_totalCapacity(self, obj):
        return obj.location.capacity if obj.location else 0

    def get_takenCapacity(self, obj):
        """Calculate taken capacity using pre-fetched data when available"""
        if not obj.location:
            return 0
            
        # Check if we have pre-calculated capacity data in the context
        capacity_data = self.context.get('capacity_data')
        if capacity_data and str(obj.location.id) in capacity_data:
            location_data = capacity_data[str(obj.location.id)]
            active_reservations = location_data['reservations']
            
            # Filter reservations that overlap with current reservation
            overlapping = [
                res for res in active_reservations
                if res['end_date'] > obj.start_date and res['start_date'] < obj.end_date
            ]
            
            # Sum up the required capacity for overlapping reservations
            return sum(res['required_capacity'] for res in overlapping)
            
        # Fallback to database query if no pre-calculated data available
        reservations = Reservation.objects.filter(
            location=obj.location,
            start_date__lt=obj.end_date,
            end_date__gt=obj.start_date,
            is_deleted=False,
            status__in=['pending', 'active']
        )
        return sum(res.required_capacity for res in reservations)

    def get_availableCapacity(self, obj):
        total_capacity = self.get_totalCapacity(obj)
        taken_capacity = self.get_takenCapacity(obj)
        return total_capacity - taken_capacity
    
    def get_actual_amount(self, obj):
        """
        Calculate actual amount without additional DB queries by using 
        the already prefetched ownership_shares
        """
        if not obj.location:
            return obj.total_amount
            
        # Direct access to prefetched ownership_shares to avoid additional query
        company_share = None
        primary_owner = None
        
        # Check cached ownership shares directly
        for share in obj.location.ownership_shares.all():
            # Find the company share
            if getattr(share, 'our_company', False):
                company_share = share
            # Find the primary owner share
            if getattr(share, 'is_primary_owner', False):
                primary_owner = share
                
        # Check if we are the primary owners
        are_we_primary_owners = company_share and primary_owner and company_share == primary_owner
        
        if not are_we_primary_owners and company_share:
            from decimal import Decimal
            # Use the percentage from the directly accessed company share
            return str(obj.total_amount * Decimal(company_share.percentage) / 100)
            
        return obj.total_amount
        
class ReservationCreateSerializer(serializers.ModelSerializer):
    contact_id = serializers.UUIDField(required=True)
    location_id = serializers.UUIDField(required=True)
    
    class Meta:
        model = Reservation
        fields = [
            'title', 'description', 'total_amount', 
            'start_date', 'end_date', 'status',
            'contact_id', 'location_id', 'notes',
            'required_capacity', 'is_deleted'
        ]
        extra_kwargs = {
            'title': {'required': True},
            'total_amount': {'required': True},
            'start_date': {'required': True},
            'end_date': {'required': True},
            'required_capacity': {'required': True}
        }
    
    def validate(self, data):
        """Validate reservation data"""
        # Validate contact exists
        try:
            contact_id = data.get('contact_id')
            if contact_id:
                contact = Contact.objects.get(pk=contact_id)
                if contact.is_deleted:
                    raise serializers.ValidationError({"contact_id": "This contact has been deleted"})
        except Contact.DoesNotExist:
            raise serializers.ValidationError({"contact_id": "Contact does not exist"})
            
        # Validate location exists
        try:
            location_id = data.get('location_id')
            if location_id:
                location = Location.objects.get(pk=location_id)
                if location.is_deleted:
                    raise serializers.ValidationError({"location_id": "This location has been deleted"})
        except Location.DoesNotExist:
            raise serializers.ValidationError({"location_id": "Location does not exist"})
            
        # Validate dates
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        if start_date and end_date:
            if end_date <= start_date:
                raise serializers.ValidationError({"end_date": "End date must be after start date"})
            
            # Check if start date is in the past
            # if start_date < timezone.now() and self.context.get('request_method') == 'POST':
            #     raise serializers.ValidationError({"start_date": "Start date cannot be in the past"})
        
        # Validate capacity
        if 'required_capacity' in data and 'location_id' in data and data['required_capacity'] > 0:
            location = Location.objects.get(pk=data['location_id'])
            if data['required_capacity'] > location.capacity:
                raise serializers.ValidationError(
                    {"required_capacity": f"Required capacity exceeds location capacity ({location.capacity})"}
                )
        
        return data
    
    def create(self, validated_data):
        return Reservation.objects.create(**validated_data)

class ReservationSimpleSerializer(serializers.ModelSerializer):
    location = serializers.SerializerMethodField()
    contact = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    totalCapacity = serializers.SerializerMethodField()
    takenCapacity = serializers.SerializerMethodField()
    availableCapacity = serializers.SerializerMethodField()
    actual_amount = serializers.SerializerMethodField()

    class Meta:
        model = Reservation
        fields = [
            'id', 'title', 'description', 'total_amount', 'start_date', 'end_date', 'actual_amount',
            'status', 'contact', 'location', 'notes', 'required_capacity',
            'status_display', 'is_deleted', 'totalCapacity', 'takenCapacity', 'availableCapacity'
        ]
        read_only_fields = fields

    def get_location(self, obj):
        if not obj.location:
            return None
        return {
            "id": str(obj.location.id),
            "name": obj.location.name,
            "address": obj.location.address,
            "capacity": obj.location.capacity,
            "is_active": obj.location.is_active
        }

    def get_contact(self, obj):
        if not obj.contact:
            return None
        return {
            "id": str(obj.contact.id),
            "name": obj.contact.name,
            "email": obj.contact.email,
            "phone": obj.contact.phone
        }

    def get_totalCapacity(self, obj):
        return obj.location.capacity if obj.location else 0

    def get_takenCapacity(self, obj):
        """Calculate taken capacity using pre-fetched data when available"""
        if not obj.location:
            return 0
            
        # Check if we have pre-calculated capacity data in the context
        capacity_data = self.context.get('capacity_data')
        if capacity_data and str(obj.location.id) in capacity_data:
            location_data = capacity_data[str(obj.location.id)]
            active_reservations = location_data['reservations']
            
            # Filter reservations that overlap with current reservation
            overlapping = [
                res for res in active_reservations
                if res['end_date'] > obj.start_date and res['start_date'] < obj.end_date
            ]
            
            # Sum up the required capacity for overlapping reservations
            return sum(res['required_capacity'] for res in overlapping)
            
        # Fallback to database query if no pre-calculated data available
        reservations = Reservation.objects.filter(
            location=obj.location,
            start_date__lt=obj.end_date,
            end_date__gt=obj.start_date,
            is_deleted=False,
            status__in=['pending', 'active']
        )
        return sum(res.required_capacity for res in reservations)

    def get_availableCapacity(self, obj):
        total_capacity = self.get_totalCapacity(obj)
        taken_capacity = self.get_takenCapacity(obj)
        return total_capacity - taken_capacity
    
    def get_actual_amount(self, obj):
        """
        Calculate actual amount without additional DB queries by using 
        the already prefetched ownership_shares
        """
        if not obj.location:
            return obj.total_amount
            
        # Direct access to prefetched ownership_shares to avoid additional query
        company_share = None
        primary_owner = None
        
        # Check cached ownership shares directly
        for share in obj.location.ownership_shares.all():
            # Find the company share
            if getattr(share, 'our_company', False):
                company_share = share
            # Find the primary owner share
            if getattr(share, 'is_primary_owner', False):
                primary_owner = share
                
        # Check if we are the primary owners
        are_we_primary_owners = company_share and primary_owner and company_share == primary_owner
        
        if not are_we_primary_owners and company_share:
            from decimal import Decimal
            # Use the percentage from the directly accessed company share
            return str(obj.total_amount * Decimal(company_share.percentage) / 100)
            
        return obj.total_amount

class ReservationDetailSerializer(serializers.ModelSerializer):
    contact = ContactSimpleSerializer(read_only=True)
    location = LocationSimpleSerializer(read_only=True)
    created_by = UserDetailSerializer(read_only=True)
    updated_by = UserDetailSerializer(read_only=True)
    history = ReservationHistorySerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    actual_amount = serializers.SerializerMethodField()

    class Meta:
        model = Reservation
        fields = [
            'id', 'title', 'description', 'total_amount', 'actual_amount',
            'start_date', 'end_date', 'status', 'status_display',
            'contact', 'location', 'notes', 'required_capacity',
            'created_at', 'updated_at', 'created_by', 'updated_by',
            'is_deleted', 'history'
        ]
        read_only_fields = fields

    def get_actual_amount(self, obj):
        if obj.location and not obj.location.are_we_owners():
            from decimal import Decimal
            return str(obj.total_amount * Decimal(obj.location.get_our_Percentage()))
        return obj.total_amount
