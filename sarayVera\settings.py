from pathlib import Path
from datetime import timedelta
from django.db import reset_queries , connection

BASE_DIR = Path(__file__).resolve().parent.parent

SECRET_KEY = "django-insecure-q77yd3srjrh#9zf9vz92qv(ogq_!*(cz$68=#qpy^1-o*iyox0"

DEBUG = True

ALLOWED_HOSTS = [
    'sarayvera-backend-production.up.railway.app',
        'localhost',
    '127.0.0.1',
]

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'users',
    'corsheaders',  # Change this line to the correct package name
    'contacts',
    'locations',
    'reservations',
    'contracts',
    'expenses',
    'income',
    'notifications'
]

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=90),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'AUTH_HEADER_TYPES': ('Bearer',),
}



MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware', 
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",  # Make sure this line is present
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    'contacts.middleware.UTF8ResponseMiddleware',  # Add our custom middleware
]

ROOT_URLCONF = "sarayVera.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "sarayVera.wsgi.application"

import os
from django.db.utils import ConnectionDoesNotExist
import dj_database_url

DATABASES = {
    'default': dj_database_url.config(
        default='postgresql://postgres:<EMAIL>:38005/railway',
        conn_max_age=600
    )
}

MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

AUTH_USER_MODEL = 'users.User'

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization settings
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_L10N = True
USE_TZ = True

# Fixed database configuration - don't overwrite the existing databases
if 'default' in DATABASES:
    # If using MySQL, ensure UTF-8 support
    if DATABASES['default'].get('ENGINE') == 'django.db.backends.mysql':
        DATABASES['default']['OPTIONS'] = DATABASES['default'].get('OPTIONS', {})
        DATABASES['default']['OPTIONS']['charset'] = 'utf8mb4'
    # If using PostgreSQL, ensure UTF-8 support (PostgreSQL uses UTF-8 by default)
    elif 'postgresql' in DATABASES['default'].get('ENGINE', ''):
        # PostgreSQL uses UTF-8 by default, but we can specify client encoding if needed
        DATABASES['default']['OPTIONS'] = DATABASES['default'].get('OPTIONS', {})
        DATABASES['default']['OPTIONS']['client_encoding'] = 'UTF8'
        
# Make sure Django Rest Framework is configured for UTF-8
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'UNICODE_JSON': True,
}

STATIC_URL = "static/"

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# CORS Settings
CORS_ALLOW_ALL_ORIGINS = True  
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = [
    'GET',
    'OPTIONS',
    'POST',
    'PUT',
    'PATCH',
    'DELETE',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

        
import json

def numOfQueriesWraper(func):

    def wrapper_func(*args, **kwargs):
        reset_queries()
        ret = func(*args, **kwargs)
        print(len(connection.queries))
        with open("queries.json", "w") as f:
            json.dump(connection.queries, f, indent=4,default=str)
        return ret

    return wrapper_func


APPEND_SLASH = False
