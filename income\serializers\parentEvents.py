from rest_framework import serializers
from income.models.parentEvents import ParentIncome
from income.models.incomeEvents import IncomeType, Income
from income.serializers.income import IncomeSimpleSerializer
from users.serializers.users import UserDetailSerializer

class ParentIncomeCreateSerializer(serializers.ModelSerializer):
    type_id = serializers.UUIDField(required=False, allow_null=True)
    
    class Meta:
        model = ParentIncome
        fields = ['title', 'amount', 'description', 'type_id']
        extra_kwargs = {
            'title': {'required': True},
            'amount': {'required': True}
        }
    
    def validate(self, data):
        # Validate type exists if provided
        type_id = data.pop('type_id', None)
        if type_id:
            try:
                income_type = IncomeType.objects.get(pk=type_id)
                data['type'] = income_type
            except IncomeType.DoesNotExist:
                raise serializers.ValidationError({"type_id": "Income type does not exist"})
        
        return data

class ParentIncomeSimpleSerializer(serializers.ModelSerializer):
    type_name = serializers.CharField(source='type.name', read_only=True)
    child_count = serializers.SerializerMethodField()
    
    class Meta:
        model = ParentIncome
        fields = [
            'id', 'title', 'amount', 
            'type_name', 'created_at', 'child_count',
            'is_deleted'
        ]
        read_only_fields = fields
    
    def get_child_count(self, obj):
        return obj.child_incomes_count()

class ParentEventDetailSerializer(serializers.ModelSerializer):
    created_by = UserDetailSerializer(read_only=True)
    updated_by = UserDetailSerializer(read_only=True)
    type = serializers.CharField(source='type.name', read_only=True)
    child_events = serializers.SerializerMethodField()
    child_count = serializers.SerializerMethodField()
    child_total = serializers.SerializerMethodField()
    dueDate = serializers.SerializerMethodField()
    category = serializers.CharField(default='income', read_only=True)  # Changed from 'expense' to 'income'
    
    class Meta:
        model = ParentIncome
        fields = [
            'id', 'title', 'amount', 'description',
            'type', 'created_at', 'updated_at',
            'created_by', 'updated_by', 'is_deleted',
            'child_events', 'child_count', 'child_total', 'dueDate', 'category'
        ]
        read_only_fields = fields
    
    def get_child_events(self, obj):
        # Use the prefetched child events
        if hasattr(obj, 'prefetched_child_events'):
            return IncomeSimpleSerializer(obj.prefetched_child_events, many=True, context=self.context).data
        # Fallback to the regular relationship if prefetched data is not available
        return IncomeSimpleSerializer(obj.child_incomes.all(), many=True).data
    
    def get_child_count(self, obj):
        # Use prefetched child events for counting
        if hasattr(obj, 'prefetched_child_events'):
            return len(obj.prefetched_child_events)
        # Fallback
        return obj.child_incomes_count()
    
    def get_child_total(self, obj):
        # Calculate from prefetched child events
        if hasattr(obj, 'prefetched_child_events'):
            return float(sum(income.amount for income in obj.prefetched_child_events))
        # Fallback
        return float(obj.child_incomes_total())
    
    def get_dueDate(self, obj):
        # Use the directly attached due date from first child
        if hasattr(obj, 'first_child_due_date'):
            return obj.first_child_due_date
        
        # Fallback
        first_child_income = obj.child_incomes.first()
        if first_child_income:
            return first_child_income.due_date
        return None