from functools import wraps
from django.http import HttpResponseForbidden
from rest_framework.response import Response
from rest_framework import status
from ..models.permissions import ModulePermissions

def check_permission(permission_class, permission_type):
    """
    Decorator to check if user has specific permission
    Args:
        permission_class: The permission class name (e.g., 'UsersPermissions')
        permission_type: The permission type to check (e.g., 'view', 'edit')
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Get user's access object
            if not hasattr(request.user, 'access'):
                return Response(
                    {'detail': 'User has no access permissions configured'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Get the permission model from ModulePermissions
            permission_model = ModulePermissions.get(permission_class)
            if not permission_model:
                return Response(
                    {'detail': f'Permission class {permission_class} not found'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            
            try:
                perm_obj = permission_model.objects.get(user_access=request.user.access)
            except permission_model.DoesNotExist:
                return Response(
                    {'detail': 'Permission record does not exist'},
                    status=status.HTTP_403_FORBIDDEN
                )
        
            if not getattr(perm_obj, permission_type, False):
                return Response(
                    {'detail': f'Missing {permission_type} permission for {permission_class}'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator