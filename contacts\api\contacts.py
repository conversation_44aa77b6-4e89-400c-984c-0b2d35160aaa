from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.core.exceptions import ValidationError
from django.db.models import Q, <PERSON>fetch, Sum, F, Decimal<PERSON>ield, Case, When, Value
from django.db import transaction, connection
from django.utils.encoding import force_str
from django.utils import timezone
from sarayVera.settings import numOfQueriesWraper
from contacts.models.contacts import Contact, ContactHistory, ContactType
from contacts.serializers.contacts import (
    ContactCreateSerializer, 
    ContactDetailSerializer,
    ContactHistorySerializer,
    ContactTypeSerializer
)
from notifications.utils import create_system_notification, delete_notifications_by_category
from locations.models.locations import Location, PartnerShare
from expenses.models.expensesEvents import Event
from income.models.incomeEvents import Income
from contracts.models.contracts import Contract  # Import Contract model
from reservations.models.reservations import Reservation  # Import Reservation model
import uuid

# Helper function to ensure proper character encoding for responses
def create_arabic_compatible_response(data, status_code=status.HTTP_200_OK):
    """
    Create a response with proper UTF-8 encoding for Arabic text
    
    Args:
        data: The data to return in the response
        status_code: The HTTP status code (default: 200 OK)
        
    Returns:
        Response object with UTF-8 charset header
    """
    response = Response(data, status=status_code)
    response['Content-Type'] = 'application/json; charset=utf-8'
    return response

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def create_contact(request):
    try:
        with transaction.atomic():  # Wrap operations in a single transaction
            # Use request.data directly - Django REST framework already handles UTF-8 encoding
            serializer = ContactCreateSerializer(data=request.data)
            if not serializer.is_valid():
                return create_arabic_compatible_response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status_code=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if email already exists and is not deleted - using values_list to reduce query size
            if 'email' in serializer.validated_data and serializer.validated_data['email']:
                email = serializer.validated_data['email']
                if Contact.objects.filter(email=email, is_deleted=False).exists():
                    return create_arabic_compatible_response(
                        {"error": "A contact with this email already exists"}, 
                        status_code=status.HTTP_400_BAD_REQUEST
                    )
            
            # Extract type_ids before saving contact - handle both field names
            type_ids = request.data.get('type_ids', None) or request.data.get('type', None)
            contact_types = []
            
            # Create the contact object (but don't save yet to avoid unnecessary queries)
            contact = serializer.build_contact(created_by=request.user)
            
            # Preload contact types if provided to reduce queries
            if type_ids:
                try:
                    # Convert to list of UUIDs
                    validated_ids = []
                    for type_id in type_ids:
                        if isinstance(type_id, str):
                            validated_ids.append(uuid.UUID(type_id))
                        else:
                            validated_ids.append(type_id)
                    
                    # Get all contact types in a single query - convert to list to materialize query
                    contact_types = list(ContactType.objects.filter(id__in=validated_ids))
                    
                    # Check if all IDs exist
                    if len(contact_types) != len(type_ids):
                        found_ids = {str(ct.id) for ct in contact_types}
                        missing_ids = set(str(id) for id in validated_ids) - found_ids
                        return Response(
                            {"error": f"Contact types with IDs {missing_ids} not found"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                        
                except Exception as e:
                    return Response(
                        {"error": "Invalid contact type IDs", "details": str(e)},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Now save the contact to get an ID
            contact.save()
            
            # Create M2M relationships in bulk if we have types instead of calling set()
            # This avoids the query to check current values since we know it's a new contact
            if contact_types:
                # Build bulk insertion values
                m2m_values = [
                    {'contact_id': contact.id, 'contacttype_id': ct.id} 
                    for ct in contact_types
                ]
                
                # Use bulk_create for the M2M through model to avoid multiple queries
                from django.db import connections
                with connections['default'].cursor() as cursor:
                    for m2m in m2m_values:
                        cursor.execute(
                            """
                            INSERT INTO contacts_contact_types (contact_id, contacttype_id) 
                            VALUES (%s, %s) ON CONFLICT DO NOTHING
                            """, 
                            [str(m2m['contact_id']), str(m2m['contacttype_id'])]
                        )
            
            # Create notification with proper encoding for Arabic messages
            create_system_notification(
                title=f"New Contact Created",
                title_ar=f"تم إنشاء جهة اتصال جديدة",
                message=f"A new contact '{force_str(contact.name)}' was created by {request.user.email}",
                message_ar=f"تم إنشاء جهة اتصال جديدة '{force_str(contact.name)}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Contact Created",
                category="contact",
                category_id=str(contact.id)
            )
            
            # Build response without additional queries
            response_data = {
                'id': contact.id,
                'name': contact.name,
                'email': contact.email,
                'phone': contact.phone,
                'company': contact.company,
                'type': [t.name for t in contact_types],  # Use in-memory list instead of querying again
                'type_ids': [str(t.id) for t in contact_types],  # Use in-memory list
                'is_deleted': contact.is_deleted,
                'balance': 0,  # New contacts always start with 0 balance
                'sharedLocations': [],  # New contacts don't have locations yet
                'ownedLocations': [], 
                'createdAt': contact.created_at,
                'createdBy': request.user.email,
            }
            # Return response with UTF-8 charset header
            return create_arabic_compatible_response(response_data, status_code=status.HTTP_201_CREATED)
            
    except Exception as e:
        return create_arabic_compatible_response(
            {"error": "Failed to create contact", "details": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_contacts(request):
    try:
        # Filter out soft-deleted contacts by default
        include_deleted = request.query_params.get('include_deleted', 'false').lower() == 'true'
        
        if include_deleted:
            contacts_query = Contact.objects.all().order_by('-created_at')
        else:
            contacts_query = Contact.objects.filter(is_deleted=False).order_by('-created_at')
        
        # Apply optimized queries with select_related and prefetch_related
        contacts = contacts_query.select_related('created_by').prefetch_related(
            'types',
            Prefetch('income_events', queryset=Income.objects.filter(is_deleted=False), to_attr='prefetched_income_events'),
            Prefetch('expenses_events', queryset=Event.objects.filter(is_deleted=False), to_attr='prefetched_expense_events'),
            Prefetch('location_shares', queryset=PartnerShare.objects.select_related('location').all(), to_attr='prefetched_location_shares'),
            Prefetch('shared_locations', queryset=Location.objects.filter(is_deleted=False), to_attr='prefetched_shared_locations')
        )
        
        # Search by name or email
        search = request.query_params.get('search')
        if search:
            contacts = contacts.filter(
                Q(name__icontains=search) | 
                Q(email__icontains=search) |
                Q(company__icontains=search)
            )
        
        # Filter by type if provided
        contact_type = request.query_params.get('type')
        if contact_type:
            contacts = contacts.filter(types__name=contact_type)
        
        # Check if we have any results
        if not contacts.exists():
            return Response({"contacts": [], "count": 0})
        
        # Get all contacts in one go to avoid lazy loading in the loop
        contacts = list(contacts)
        
        data = []
        for contact in contacts:
            # Directly calculate balance from prefetched data
            total_income = sum(income.amount for income in contact.prefetched_income_events)
            total_expenses = sum(expense.amount for expense in contact.prefetched_expense_events)
            balance = float(total_income - total_expenses)
            
            # Get owned locations via PartnerShare where is_primary_owner=True
            owned_locations = [
                ps.location for ps in getattr(contact, 'prefetched_location_shares', [])
                if ps.is_primary_owner and not ps.location.is_deleted
            ]
            shared_locations = getattr(contact, 'prefetched_shared_locations', [])
            
            data.append({
                    'id': contact.id,
                    'name': contact.name,
                    'email': contact.email,
                    'phone': contact.phone,
                    'company': contact.company,
                    'type': [t.name for t in contact.types.all()],  # This uses prefetched types
                    'is_deleted': contact.is_deleted,
                    'balance': balance,
                    'sharedLocations': [
                        {
                            'id': loc.id,
                            'name': loc.name,
                            'address': loc.address,
                            'capacity': loc.capacity,
                            'is_active': loc.is_active
                        } for loc in shared_locations
                    ],  
                    'ownedLocations': [
                        {
                            'id': loc.id,
                            'name': loc.name,
                            'address': loc.address,
                            'capacity': loc.capacity,
                            'is_active': loc.is_active
                        } for loc in owned_locations
                    ],    
                    'createdAt': contact.created_at,
                    'createdBy': contact.created_by.email if contact.created_by else None,
                })
        return create_arabic_compatible_response({"contacts": data, "count": len(data)})
    except Exception as e:
        return create_arabic_compatible_response(
            {"error": "Failed to retrieve contacts", "details": str(e)},
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_contact(request, contact_id):
    try:
        contact = get_object_or_404(Contact, id=contact_id)
        
        serializer = ContactDetailSerializer(contact)
        return Response(serializer.data)
    except Contact.DoesNotExist:
        return Response(
            {"error": f"Contact with ID {contact_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve contact", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def update_contact(request, contact_id):
    try:
        with transaction.atomic():
            contact = get_object_or_404(Contact, id=contact_id)
            
            # Capture the previous state before making changes
            previous_data = contact.to_dict()
            
            serializer = ContactCreateSerializer(
                contact, 
                data=request.data, 
                partial=True
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if email is being changed to an existing one
            if 'email' in serializer.validated_data and serializer.validated_data['email']:
                new_email = serializer.validated_data['email']
                if (new_email != contact.email and 
                    Contact.objects.filter(email=new_email, is_deleted=False).exists()):
                    return Response(
                        {"error": "Another contact with this email already exists"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Handle type_ids if provided
            type_ids = request.data.get('type_ids', None)
            if type_ids is not None:
                try:
                    contact.set_types_by_ids(type_ids)
                except Exception as e:
                    return Response(
                        {"error": "Invalid contact type IDs", "details": str(e)},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Handle old 'type' field for backward compatibility
            elif 'type' in serializer.validated_data:
                type_list = serializer.validated_data.pop('type')
                contact.set_types(type_list)
            
            for key, value in serializer.validated_data.items():
                setattr(contact, key, value)
            
            contact.save()
            
            contact.create_history_record(previous_data, request.user)
            
            # Create notification for contact update
            changes = []
            current_data = contact.to_dict()
            
            # Check key fields for changes
            if previous_data.get('name') != current_data.get('name'):
                changes.append(f"name changed from '{previous_data.get('name')}' to '{current_data.get('name')}'")
            
            if previous_data.get('email') != current_data.get('email'):
                changes.append(f"email changed from '{previous_data.get('email')}' to '{current_data.get('email')}'")
            
            if previous_data.get('phone') != current_data.get('phone'):
                changes.append(f"phone changed")
                
            if previous_data.get('company') != current_data.get('company'):
                changes.append(f"company changed")
                
            # Check if types changed
            prev_types = {t['name'] for t in previous_data.get('types', [])}
            curr_types = {t['name'] for t in current_data.get('types', [])}
            
            if prev_types != curr_types:
                changes.append("contact types changed")
            
            change_description = ", ".join(changes) if changes else "details updated"
            
            create_system_notification(
                title=f"Contact '{contact.name}' Updated",
                title_ar=f"تم تحديث جهة الاتصال '{contact.name}'",
                message=f"The contact '{contact.name}' was updated by {request.user.email}: {change_description}",
                message_ar=f"تم تحديث جهة الاتصال '{contact.name}' بواسطة {request.user.email}: {change_description}",
                priority="low",
                notification_type_name="Contact Update",
                category="contact",
                category_id=str(contact.id)
            )
            
            return Response(ContactDetailSerializer(contact).data)
    except Contact.DoesNotExist:
        return Response(
            {"error": f"Contact with ID {contact_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to update contact", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def soft_delete_contact(request, contact_id):
    """Soft delete a contact by setting is_deleted to True"""
    try:
        with transaction.atomic():  # Wrap in transaction to ensure all or nothing
            # Get current datetime for comparing with end dates
            current_datetime = timezone.now()
            
            # Get the contact
            contact = get_object_or_404(Contact, id=contact_id)
            
            # Check if contact is already deleted
            if contact.is_deleted:
                return Response(
                    {"warning": f"Contact '{contact.name}' is already marked as deleted"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check for location partnerships - fetch any active location partnerships
            location_partnerships = PartnerShare.objects.filter(
                contact=contact,
                location__is_deleted=False  # Only consider active locations
            ).select_related('location')
            
            if location_partnerships.exists():
                # Build a list of location details where this contact is a partner
                partnerships = []
                location_names = []  # To collect location names for the warning message
                
                for partnership in location_partnerships:
                    location = partnership.location
                    location_names.append(location.name)
                    partnerships.append({
                        'location_id': str(location.id),
                        'location_name': location.name,
                        'percentage': float(partnership.percentage),
                        'is_primary_owner': partnership.is_primary_owner,
                        'address': location.address,
                        'is_active': location.is_active
                    })
                
                # Create a formatted list of location names for the warning message
                if len(location_names) == 1:
                    locations_text = f"location '{location_names[0]}'"
                elif len(location_names) == 2:
                    locations_text = f"locations '{location_names[0]}' and '{location_names[1]}'"
                else:
                    locations_text = "locations " + ", ".join([f"'{name}'" for name in location_names[:-1]]) + f" and '{location_names[-1]}'"
                
                return Response({
                    "warning": f"Contact '{contact.name}' was not deleted because they have ownership shares in {locations_text}",
                    "deleted": False,
                    "location_partnerships": partnerships,
                    "location_names": location_names
                }, status=status.HTTP_409_CONFLICT)
            
            # Check for upcoming income events associated with this contact
            upcoming_income = Income.objects.filter(
                contact=contact,
                is_deleted=False,
                status__in=['pending', 'upcoming', 'overdue'],
                due_date__gt=current_datetime,
                received_date__isnull=True
            ).select_related('type').order_by('due_date')
            
            # Check for upcoming expense events associated with this contact
            upcoming_expenses = Event.objects.filter(
                contact=contact,
                is_deleted=False,
                status__in=['pending', 'upcoming', 'overdue'],
                due_date__gt=current_datetime,
                paid_date__isnull=True
            ).select_related('type').order_by('due_date')
            
            # If either upcoming income or expenses exist, return a warning but don't delete
            if upcoming_income.exists() or upcoming_expenses.exists():
                # Build lists of upcoming transactions
                income_items = [{
                    'id': str(income.id),
                    'title': income.title,
                    'amount': float(income.amount),
                    'due_date': income.due_date.isoformat(),
                    'type': income.type.name if income.type else None,
                    'status': income.status
                } for income in upcoming_income]
                
                expense_items = [{
                    'id': str(expense.id),
                    'title': expense.title,
                    'amount': float(expense.amount),
                    'due_date': expense.due_date.isoformat(),
                    'type': expense.type.name if expense.type else None,
                    'status': expense.status
                } for expense in upcoming_expenses]
                
                # Create formatted strings of transaction names
                income_names = [income.title for income in upcoming_income]
                expense_names = [expense.title for expense in upcoming_expenses]
                
                # Build human-readable warning message with transaction details
                warning_message = f"Contact '{contact.name}' was not deleted because there are upcoming transactions associated with this contact"
                
                # Add income details if any
                if income_names:
                    if len(income_names) == 1:
                        warning_message += f"\n\nIncome: '{income_names[0]}'"
                    elif len(income_names) == 2:
                        warning_message += f"\n\nIncomes: '{income_names[0]}' and '{income_names[1]}'"
                    else:
                        warning_message += "\n\nIncomes: " + ", ".join([f"'{name}'" for name in income_names[:-1]]) + f" and '{income_names[-1]}'"
                
                # Add expense details if any
                if expense_names:
                    if len(expense_names) == 1:
                        warning_message += f"\n\nExpense: '{expense_names[0]}'"
                    elif len(expense_names) == 2:
                        warning_message += f"\n\nExpenses: '{expense_names[0]}' and '{expense_names[1]}'"
                    else:
                        warning_message += "\n\nExpenses: " + ", ".join([f"'{name}'" for name in expense_names[:-1]]) + f" and '{expense_names[-1]}'"
                
                return Response({
                    "warning": warning_message,
                    "deleted": False,
                    "upcoming_income": income_items,
                    "upcoming_expenses": expense_items,
                    "income_names": income_names,  # Added for convenience
                    "expense_names": expense_names  # Added for convenience
                }, status=status.HTTP_409_CONFLICT)
            
            # If we're here, we can proceed with the deletion
            
            # Now load prefetches for contracts and reservations
            contact = Contact.objects.prefetch_related(
                # Prefetch contracts and related objects
                Prefetch(
                    'contracts',
                    queryset=Contract.objects.filter(is_deleted=False).select_related(
                        'location', 
                        'renewal_terms'
                    ).prefetch_related(
                        'documents'
                    ),
                    to_attr='prefetched_contracts'
                ),
                # Prefetch reservations and related objects
                Prefetch(
                    'reservations',
                    queryset=Reservation.objects.filter(is_deleted=False).select_related(
                        'location'
                    ),
                    to_attr='prefetched_reservations'
                )
            ).get(id=contact_id)
            
            # Save contact name for response
            contact_name = contact.name
            
            # Process contracts - Use the prefetched contracts instead of making a new query
            # Only delete contracts with end_date in the future
            deleted_contract_count = 0
            for contract in getattr(contact, 'prefetched_contracts', []):
                # Only soft delete contracts with future end dates
                if contract.end_date > current_datetime:
                    # Delete old notifications for this contract before creating new ones
                    delete_notifications_by_category("contract", str(contract.id))
                    
                    # Convert contract data to dictionary once before soft delete
                    contract_data = contract.to_dict()
                    
                    # Set is_deleted=True directly
                    contract.is_deleted = True
                    contract.save(update_fields=['is_deleted'])
                    
                    # Create history record with the pre-collected data
                    contract.create_history_record(contract_data, request.user)
                    
                    deleted_contract_count += 1
                    
                    # Create notification for contract deletion
                    create_system_notification(
                        title=f"Contract '{contract.title}' Deleted",
                        title_ar=f"تم حذف العقد '{contract.title}'",
                        message=f"The contract '{contract.title}' was automatically soft-deleted because its associated contact '{contact_name}' was deleted by {request.user.email}",
                        message_ar=f"تم حذف العقد '{contract.title}' تلقائيًا لأن جهة الاتصال المرتبطة بها '{contact_name}' تم حذفها بواسطة {request.user.email}",
                        priority="medium",
                        notification_type_name="Contract Deletion",
                        category="contract",
                        category_id=str(contract.id)
                    )
            
            # Process reservations - Use the prefetched reservations instead of making a new query
            # Only delete reservations with end_date in the future
            deleted_reservation_count = 0
            for reservation in getattr(contact, 'prefetched_reservations', []):
                # Only soft delete reservations with future end dates
                if reservation.end_date > current_datetime:
                    # Delete old notifications for this reservation before creating new ones
                    delete_notifications_by_category("reservation", str(reservation.id))
                    
                    # Convert reservation data to dictionary once before soft delete
                    reservation_data = reservation.to_dict()
                    
                    # Set is_deleted=True and status=CANCELLED directly
                    reservation.is_deleted = True
                    reservation.status = Reservation.ReservationStatus.CANCELLED
                    reservation.save(update_fields=['is_deleted', 'status'])
                    
                    # Create history record with the pre-collected data
                    reservation.create_history_record(reservation_data, request.user)
                    
                    deleted_reservation_count += 1
                    
                    # Create notification for reservation deletion
                    create_system_notification(
                        title=f"Reservation '{reservation.title}' Cancelled",
                        title_ar=f"تم إلغاء الحجز '{reservation.title}'",
                        message=f"The reservation '{reservation.title}' was automatically cancelled because its associated contact '{contact_name}' was deleted by {request.user.email}",
                        message_ar=f"تم إلغاء الحجز '{reservation.title}' تلقائيًا لأن جهة الاتصال المرتبطة بها '{contact_name}' تم حذفها بواسطة {request.user.email}",
                        priority="medium",
                        notification_type_name="Reservation Cancellation",
                        category="reservation",
                        category_id=str(reservation.id)
                    )
            
            # Delete old notifications for this contact before creating new ones
            delete_notifications_by_category("contact", str(contact.id))
            
            # Use the soft_delete method from Contact model
            contact.soft_delete(request.user)
            
            # Create notification for contact deletion
            create_system_notification(
                title=f"Contact '{contact_name}' Deleted",
                title_ar=f"تم حذف جهة الاتصال '{contact_name}'",
                message=f"The contact '{contact_name}' was soft-deleted by {request.user.email}",
                message_ar=f"تم حذف جهة الاتصال '{contact_name}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Contact Deletion",
                category="contact",
                category_id=str(contact.id)
            )
            
            # Build response message
            response_message = f"Contact '{contact_name}' has been marked as deleted"
            if deleted_contract_count > 0 or deleted_reservation_count > 0:
                response_message += " along with "
                
                deletions = []
                if deleted_contract_count > 0:
                    deletions.append(f"{deleted_contract_count} active contract(s)")
                if deleted_reservation_count > 0:
                    deletions.append(f"{deleted_reservation_count} upcoming reservation(s)")
                    
                response_message += " and ".join(deletions)
            
            return Response({
                "message": response_message,
                "deleted": True
            }, status=status.HTTP_200_OK)
    except Contact.DoesNotExist:
        return Response(
            {"error": f"Contact with ID {contact_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete contact", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def permanent_delete_contact(request, contact_id):
    """Permanently delete contact from database"""
    try:
        contact = get_object_or_404(Contact, id=contact_id)
        
        # Get confirmation from request if needed
        confirm = request.query_params.get('confirm', 'false').lower() == 'true'
        if not confirm:
            return Response(
                {"warning": "This will permanently delete the contact. Set confirm=true to proceed."}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Save contact name for response
        contact_name = contact.name
        
        # Permanently delete the contact
        contact.delete()
        
        return Response({
            "message": f"Contact '{contact_name}' has been permanently deleted"
        }, status=status.HTTP_200_OK)
    except Contact.DoesNotExist:
        return Response(
            {"error": f"Contact with ID {contact_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete contact", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def get_contact_history(request, contact_id):
    """Retrieve history records for a specific contact"""
    try:
        # First check if contact exists
        contact = get_object_or_404(Contact, id=contact_id)
        
        # Get all history records for this contact
        history_records = ContactHistory.objects.filter(contact=contact).order_by('-modified_at')
        
        # Handle case of no history records
        if not history_records.exists():
            return Response({
                "contact_id": contact_id,
                "contact_name": contact.name,
                "history": [],
                "message": "No history records found for this contact"
            })
        
        # Serialize the history records
        serializer = ContactHistorySerializer(history_records, many=True)
        
        return Response({
            "contact_id": contact_id,
            "contact_name": contact.name,
            "history": serializer.data,
            "count": history_records.count()
        })
    except Contact.DoesNotExist:
        return Response(
            {"error": f"Contact with ID {contact_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve contact history", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def list_contact_types(request):
    """List all contact types"""
    try:
        contact_types = ContactType.objects.all()
        serializer = ContactTypeSerializer(contact_types, many=True)
        return Response({"contact_types": serializer.data, "count": contact_types.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve contact types", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def create_contact_type(request):
    """Create a new contact type"""
    try:
        serializer = ContactTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if type with same name already exists
        name = serializer.validated_data['name']
        if ContactType.objects.filter(name__iexact=name).exists():
            return Response(
                {"error": f"Contact type '{name}' already exists"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create new contact type
        contact_type = serializer.save(created_by=request.user)
        
        return Response(
            ContactTypeSerializer(contact_type).data,
            status=status.HTTP_201_CREATED
        )
    except Exception as e:
        return Response(
            {"error": "Failed to create contact type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def delete_contact_type(request, type_id):
    """Delete a contact type"""
    try:
        contact_type = get_object_or_404(ContactType, id=type_id)
        
        # Check if type is used by any contacts
        if contact_type.contacts.exists():
            return Response(
                {"error": f"Cannot delete contact type '{contact_type.name}' as it is being used by contacts"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Store type name for response
        type_name = contact_type.name
        
        # Delete the type
        contact_type.delete()
        
        return Response({
            "message": f"Contact type '{type_name}' has been deleted"
        }, status=status.HTTP_200_OK)
    except ContactType.DoesNotExist:
        return Response(
            {"error": f"Contact type with ID {type_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete contact type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def get_contact_finances(request):
    """
    Retrieve all income and expense events associated with a specific contact.
    
    Expects a POST request with contact_id in the body.
    Returns a structured response with income and expense data.
    """
    try:
        # Get contact_id from request body
        contact_id = request.data.get('contact_id')
        if not contact_id:
            return Response(
                {"error": "contact_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            contact = Contact.objects.get(id=contact_id)
        except Contact.DoesNotExist:
            return Response(
                {"error": f"Contact with ID {contact_id} not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get income data using the internal function
        from income.api.income import get_income_by_contact_internal
        income_data = get_income_by_contact_internal(contact_id)
        
        # Get expense data using the internal function
        from expenses.api.expenses import get_expense_by_contact_internal
        expense_data = get_expense_by_contact_internal(contact_id)
        
        # Calculate net amount
        total_income = income_data.get('total', 0)
        total_expenses = expense_data.get('total', 0)
        net_amount = total_income - total_expenses
        
        # Return structured response
        return Response({
            "contact": {
                "id": str(contact.id),
                "name": contact.name,
                "email": contact.email,
                "phone": contact.phone,
                "company": contact.company
            },
            "finances": {
                "income": {
                    "events": income_data.get('incomes', []),
                    "count": income_data.get('count', 0),
                    "total": total_income
                },
                "expenses": {
                    "events": expense_data.get('expenses', []),
                    "count": expense_data.get('count', 0),
                    "total": total_expenses
                },
                "net_amount": float(net_amount)
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve financial data for contact", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

