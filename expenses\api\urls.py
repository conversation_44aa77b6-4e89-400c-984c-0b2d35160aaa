from django.urls import path
from expenses.api.expenses import (
    create_event,
    list_events,
    get_event,
    update_event,
    delete_event,
    get_event_history,
    mark_event_paid,
    list_event_types,
    create_event_type,
    delete_event_type,
    list_all_expenses,
    list_expenses_by_location,
    list_expenses_by_contact,
    create_events,
    soft_delete_expense,
    update_event_status,  # Add this import
    
)
from expenses.api.parentEvents import (
    create_parent_event,
    list_parent_events,
    get_parent_event,
    update_parent_event,
    delete_parent_event,
    create_parent_with_children,
    
)

urlpatterns = [
    # Existing event endpoints
    path('getall/', list_events, name='list_events'),
    path('create/', create_event, name='create_event'),
    path('getevent/<uuid:event_id>/', get_event, name='get_event'),
    path('updateevent/<uuid:event_id>/', update_event, name='update_event'),
    path('deleteevent/<uuid:event_id>/', delete_event, name='delete_event'),
    path('history/<uuid:event_id>/', get_event_history, name='get_event_history'),
    path('markeventpaid/<uuid:event_id>/', mark_event_paid, name='mark_event_paid'),
    path('listall/', list_all_expenses, name='list_all_expenses'),
    path('createMultiple/', create_events, name='create_events'),
    path('softdelete/<uuid:expense_id>/', soft_delete_expense, name='soft_delete_expense'),
    # New endpoints for event types
    path('types/', list_event_types, name='list_event_types'),
    path('types/create/', create_event_type, name='create_event_type'),
    path('types/delete/<uuid:type_id>/', delete_event_type, name='delete_event_type'),
    
    # New endpoints for parent events
    path('parent/create/', create_parent_event, name='create_parent_event'),
    path('parent/getall/', list_parent_events, name='list_parent_events'),
    path('parent/get/<uuid:parent_id>/', get_parent_event, name='get_parent_event'),
    path('parent/update/<uuid:parent_id>/', update_parent_event, name='update_parent_event'),
    path('parent/delete/<uuid:parent_id>/', delete_parent_event, name='delete_parent_event'),
    path('parent/create-with-children/', create_parent_with_children, name='create_parent_with_children'),
    
    # New endpoints for filtered expenses
    path('by-location/', list_expenses_by_location, name='expenses_by_location'),
    path('by-contact/', list_expenses_by_contact, name='expenses_by_contact'),
    path('updatestatus/', update_event_status, name='update_event_status'),
]
