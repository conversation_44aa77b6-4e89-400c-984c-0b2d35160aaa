from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.db.models import Q
from django.db import transaction
from django.utils import timezone
import uuid

from income.models.incomeEvents import Income, IncomeHistory, IncomeType
from income.serializers.income import (
    IncomeCreateSerializer,
    IncomeDetailSerializer,
    IncomeSimpleSerializer,
    IncomeHistorySerializer,
    IncomeTypeSerializer
)
from income.models.parentEvents import ParentIncome
from income.models.incomeEvents import Income, IncomeType
from income.serializers.parentEvents import (
    ParentIncomeCreateSerializer,
    ParentEventDetailSerializer,
    ParentIncomeSimpleSerializer
)
from sarayVera.settings import numOfQueriesWraper
from locations.models.locations import Location
from locations.serializers.locations import LocationMinimalSerializer
from contacts.models.contacts import Contact , OurCompany
from expenses.serializers.expenses import EventCreateSerializer
from notifications.utils import create_system_notification, delete_notifications_by_category

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_all_incomes(request):
    """
    Retrieve all incomes (parent events with children and normal events) sorted by date.
    """
    try:
        all_child_incomes = {}

        # Fetch parent incomes
        parent_events = ParentIncome.objects.filter(
            is_deleted=False
        ).select_related(
            'created_by', 'updated_by', 'type'
        ).order_by('-created_at')

        parent_ids = list(parent_events.values_list('id', flat=True))

        # Fetch all incomes (children + normal)
        all_incomes = Income.objects.filter(
            Q(parent__in=parent_ids, is_deleted=False) |
            Q(parent__isnull=True, is_deleted=False)
        ).select_related(
            'type', 'created_by', 'updated_by', 'parent', 'reservation', 'contact', 'location'
        ).prefetch_related(
            'location__ownership_shares'  # Prefetch the ownership shares
        )

        # Separate normal and child incomes
        normal_incomes = []
        for income in all_incomes:
            if income.parent_id:
                all_child_incomes.setdefault(income.parent_id, []).append(income)
            else:
                normal_incomes.append(income)

        # Collect all location IDs from incomes
        location_ids = set()
        for income in all_incomes:
            if income.location_id:
                location_ids.add(str(income.location_id))
                
        # Pre-process location ownership data
        for income in all_incomes:
            if income.location:
                # Cache calculated values on the location object
                location = income.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Prefetch history
        history_map = {}
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT ih.income_id, ih.modified_at, ih.id AS history_id, u.id AS user_id, u.username, u.email, u.role
                FROM income_incomehistory ih
                LEFT JOIN users_user u ON ih.modified_by_id = u.id
                WHERE ih.income_id = ANY(%s)
                ORDER BY ih.modified_at DESC
            """, [list(all_incomes.values_list('id', flat=True))])
            for row in cursor.fetchall():
                income_id, modified_at, history_id, user_id, username, user_email, user_role = row
                history_map.setdefault(income_id, []).append({
                    'id': history_id,
                    'modified_at': modified_at,
                    'modified_by': username,
                    'user': {
                        'id': user_id,
                        'username': username,
                        'email': user_email,
                        'role': user_role
                    } if user_id else None
                })

        # Attach prefetched data
        for parent in parent_events:
            parent.prefetched_child_events = all_child_incomes.get(parent.id, [])
            parent.first_child_due_date = (
                sorted(parent.prefetched_child_events, key=lambda x: x.due_date)[0].due_date
                if parent.prefetched_child_events else None
            )

            for child in parent.prefetched_child_events:
                child.prefetched_history = history_map.get(child.id, [])

        for income in normal_incomes:
            income.prefetched_history = history_map.get(income.id, [])

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()

        context = {'prefetched_data': True}
        parent_serializer = ParentEventDetailSerializer(parent_events, many=True, context=context)
        normal_serializer = IncomeSimpleSerializer(normal_incomes, many=True, context=context)

        # Unpatch methods
        LocationPatch.unpatch(originals)

        combined_incomes = parent_serializer.data + normal_serializer.data
        combined_incomes.sort(key=lambda x: x.get('created_at', ''))

        return Response({"incomes": combined_incomes, "count": len(combined_incomes)}, status=status.HTTP_200_OK)

    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return Response(
            {"error": "Failed to retrieve incomes", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_income(request):
    try:
        with transaction.atomic():
            request_data = request.data.copy()
            
            # Check if we should use percentage-based calculation
            # Accept both spellings for backward compatibility
            isTotalAmount = request_data.get("isTotalAmount", False) or request_data.get("is_percatage", False)
            
            if isTotalAmount and request_data.get("location_id"):
                location_id = request_data.get("location_id")
                try:
                    location = Location.objects.prefetch_related("ownership_shares").get(id=location_id)
                    # Get our company's share percentage
                    our_share = location.ownership_shares.filter(our_company=True).first()
                    
                    if our_share:
                        original_amount = float(request_data.get("amount", 0))
                        percentage = float(our_share.percentage) / 100.0  # Convert percentage to decimal
                        # Adjust the amount based on our percentage
                        calculated_amount = round(original_amount * percentage, 2)
                        print(f"Percentage calculation: {original_amount} * {our_share.percentage}% = {calculated_amount}")
                        request_data["amount"] = calculated_amount
                    else:
                        print("No company share found for this location")
                except Exception as percentage_error:
                    return Response(
                        {"error": "Failed to calculate percentage-based amount", 
                         "details": str(percentage_error)},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            serializer = IncomeCreateSerializer(data=request_data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            income = serializer.save(
                created_by=request.user,
                updated_by=request.user
            )

            # Create notification for the new income
            from notifications.utils import create_system_notification
            create_system_notification(
                title=f"New Income Created: {income.title}",
                title_ar=f"تم إنشاء دخل جديد: {income.title}",
                message=f"A new income '{income.title}' for {income.amount} was created by {request.user.email}",
                message_ar=f"تم إنشاء دخل جديد '{income.title}' بمبلغ {income.amount} بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Income Created",
                category="income",
                category_id=str(income.id) if income.id else None,
            )

            # If notification date is provided, create a reminder
            notification_view_date = request.data.get('notification_date')
            if notification_view_date:
                from django.utils.dateparse import parse_datetime
                from django.utils import timezone as django_timezone
                try:
                    notification_date = parse_datetime(notification_view_date)
                    if notification_date:
                        # Ensure both dates are timezone-aware for proper comparison
                        due_date = income.due_date
                        
                        # If notification_date is naive, make it timezone-aware
                        if notification_date.tzinfo is None:
                            notification_date = django_timezone.make_aware(notification_date)
                        
                        # If due_date is naive, make it timezone-aware
                        if due_date.tzinfo is None:
                            due_date = django_timezone.make_aware(due_date)
                        
                        # Convert both to the same timezone for comparison
                        notification_date = django_timezone.localtime(notification_date)
                        due_date = django_timezone.localtime(due_date)
                        
                        # Now perform the comparison safely
                        days_difference = (due_date.date() - notification_date.date()).days if notification_date < due_date else 0
                        reminder_message = f"You should collect {income.amount} in {days_difference} days." if days_difference > 0 else f"You need to collect {income.amount} today or it will be overdue."
                        reminder_message_ar = f"يجب عليك جمع {income.amount} في {days_difference} يومًا." if days_difference > 0 else f"يجب عليك جمع {income.amount} اليوم أو سيتأخر."
                        create_system_notification(
                            title=f"Upcoming Income Due: {income.title}",
                            title_ar=f"الدخل المستحق قريبًا: {income.title}",
                            message=reminder_message,
                            message_ar=reminder_message_ar,
                            priority="medium",
                            notification_type_name="Income Reminder",
                            view_date=notification_date,
                            category="income",
                            category_id=str(income.id) if income.id else None
                        )
                except Exception as date_error:
                    print(f"Error processing notification date: {str(date_error)}")
                    # Log the specific timezone states for debugging
                    if 'notification_date' in locals():
                        print(f"notification_date timezone: {notification_date.tzinfo}")
                    print(f"due_date timezone: {income.due_date.tzinfo}")

            if request.data.get("autoCreate", True) is True:
                location_id = request.data.get("location_id")
                if not location_id:
                    raise Exception("Location ID is required for auto expense creation.")

                location = Location.objects.prefetch_related("ownership_shares__contact").get(id=location_id)
                if (location.are_we_owners()):
                    our_id = OurCompany.get_instance().id
                    for share in location.ownership_shares.all():
                        if share.contact.id == our_id:
                            continue
                        percentage = float(share.percentage)
                        expense_amount = round((percentage / 100.0) * float(income.amount), 2)
                        expense_data = {
                            "title": f"{income.title} - Share of {share.contact.name}",
                            "amount": expense_amount,
                            "due_date": income.due_date,
                            "description": f"Auto-created expense for {share.contact.name} from income {income.id}",
                            "status": "upcoming",
                            "priority": "medium",
                            "contact_id": str(share.contact.id),
                            "location_id": str(location.id),
                            "type_id": request.data.get("type_id"),
                            "reservation_id": request.data.get("reservation_id"),
                            "parent_id": request.data.get("parent_id")
                        }

                        expense_serializer = EventCreateSerializer(data=expense_data)
                        if expense_serializer.is_valid():
                            expense_serializer.save(created_by=request.user, updated_by=request.user)
                        else:
                            raise Exception({
                                "auto_created_expense_error": expense_serializer.errors,
                                "contact": share.contact.id
                            })

            detail_serializer = IncomeDetailSerializer(income)
            return Response(detail_serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response(
            {"error": "Failed to create income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_multiple_incomes(request):
    try:
        with transaction.atomic():
            results = []
            errors = []

            incomes_data = request.data.get("incomes")
            if not incomes_data or not isinstance(incomes_data, list):
                return Response({"error": "Expected 'incomes' to be a list."}, status=status.HTTP_400_BAD_REQUEST)

            from notifications.utils import create_system_notification
            from django.utils.dateparse import parse_datetime

            for index, income_data in enumerate(incomes_data):
                # Create a copy of the data to modify if needed
                income_data_copy = income_data.copy()
                
                # Check if we should use percentage-based calculation
                # Accept both spellings for backward compatibility
                isTotalAmount = income_data_copy.get("isTotalAmount", False) or income_data_copy.get("is_percatage", False)
                
                if isTotalAmount and income_data_copy.get("location_id"):
                    location_id = income_data_copy.get("location_id")
                    try:
                        location = Location.objects.prefetch_related("ownership_shares").get(id=location_id)
                        # Get our company's share percentage
                        our_share = location.ownership_shares.filter(our_company=True).first()
                        
                        if our_share:
                            original_amount = float(income_data_copy.get("amount", 0))
                            percentage = float(our_share.percentage) / 100.0  # Convert percentage to decimal
                            # Adjust the amount based on our percentage
                            calculated_amount = round(original_amount * percentage, 2)
                            print(f"Percentage calculation: {original_amount} * {our_share.percentage}% = {calculated_amount}")
                            income_data_copy["amount"] = calculated_amount
                        else:
                            print(f"No company share found for location {location_id}")
                    except Exception as percentage_error:
                        errors.append({
                            "index": index,
                            "error": "Failed to calculate percentage-based amount",
                            "details": str(percentage_error)
                        })
                        continue
                
                serializer = IncomeCreateSerializer(data=income_data_copy)
                if not serializer.is_valid():
                    errors.append({
                        "error": "Invalid data",
                        "details": serializer.errors,
                        "income_data": income_data
                    })
                    continue

                income = serializer.save(
                    created_by=request.user,
                    updated_by=request.user
                )

                create_system_notification(
                    title=f"New Income Created: {income.title}",
                    title_ar=f"تم إنشاء دخل جديد: {income.title}",
                    message=f"A new income '{income.title}' for {income.amount} was created by {request.user.email}",
                    message_ar=f"تم إنشاء دخل جديد '{income.title}' بمبلغ {income.amount} بواسطة {request.user.email}",
                    priority="medium",
                    notification_type_name="Income Created",
                    category="income",
                    category_id=str(income.id) if income.id else None,
                )

                notification_view_date = income_data.get('notification_date')
                if notification_view_date:
                    try:
                        notification_date = parse_datetime(notification_view_date)
                        if notification_date:
                            days_difference = (income.due_date - notification_date).days if notification_date < income.due_date else 0
                            reminder_message = (
                                f"You should collect {income.amount} in {days_difference} days."
                                if days_difference > 0
                                else f"You need to collect {income.amount} today or it will be overdue."
                            )
                            reminder_message_ar = (
                                f"يجب عليك جمع {income.amount} في {days_difference} يومًا."
                                if days_difference > 0
                                else f"يجب عليك جمع {income.amount} اليوم أو سيتأخر."
                            )
                            create_system_notification(
                                title=f"Upcoming Income Due: {income.title}",
                                title_ar=f"الدخل المستحق قريبًا: {income.title}",
                                message=reminder_message,
                                message_ar=reminder_message_ar,
                                priority="medium",
                                notification_type_name="Income Reminder",
                                view_date=notification_date,
                                category="income",
                                category_id=str(income.id) if income.id else None
                            )
                    except Exception as date_error:
                        print(f"Error processing notification date: {str(date_error)}")

                if income_data.get("autoCreate", True) is True:
                    location_id = income_data.get("location_id")
                    if not location_id:
                        errors.append({
                            "error": "Location ID is required for auto expense creation.",
                            "income_id": income.id
                        })
                        continue

                    location = Location.objects.prefetch_related("ownership_shares__contact").get(id=location_id)
                    if location.are_we_owners():
                        our_id = OurCompany.get_instance().id
                        for share in location.ownership_shares.all():
                            if share.contact.id == our_id:
                                continue
                            percentage = float(share.percentage)
                            expense_amount = round((percentage / 100.0) * float(income.amount), 2)
                            expense_data = {
                                "title": f"{income.title} - Share of {share.contact.name}",
                                "amount": expense_amount,
                                "due_date": income.due_date,
                                "description": f"Auto-created expense for {share.contact.name} from income {income.id}",
                                "status": "upcoming",
                                "priority": "medium",
                                "contact_id": str(share.contact.id),
                                "location_id": str(location.id),
                                "type_id": income_data.get("type_id"),
                                "reservation_id": income_data.get("reservation_id"),
                                "parent_id": income_data.get("parent_id")
                            }

                            expense_serializer = EventCreateSerializer(data=expense_data)
                            if expense_serializer.is_valid():
                                expense_serializer.save(created_by=request.user, updated_by=request.user)
                            else:
                                errors.append({
                                    "auto_created_expense_error": expense_serializer.errors,
                                    "contact": share.contact.id,
                                    "income_id": income.id
                                })

                detail_serializer = IncomeDetailSerializer(income)
                results.append(detail_serializer.data)

            response_data = {"created_incomes": results}
            if errors:
                response_data["errors"] = errors

            return Response(response_data, status=status.HTTP_207_MULTI_STATUS if errors else status.HTTP_201_CREATED)

    except Exception as e:
        return Response(
            {"error": "Failed to create incomes", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_income(request, income_id):
    try:
        income = get_object_or_404(Income, id=income_id)
        serializer = IncomeDetailSerializer(income)
        return Response(serializer.data)
    except Income.DoesNotExist:
        return Response(
            {"error": f"Income with ID {income_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def update_income(request, income_id):
    try:
        with transaction.atomic():
            income = get_object_or_404(Income.objects.select_related('reservation', 'contract'), id=income_id)
            
            # Check if the income is linked to a reservation
            if income.reservation_id is not None:
                reservation = income.reservation  # Already loaded via select_related
                
                return Response({
                    "error": f"Income '{income.title}' cannot be updated because it is linked to reservation '{reservation.title}'",
                    "can_update": False,
                    "reservation": {
                        "id": str(reservation.id),
                        "title": reservation.title,
                        "start_date": reservation.start_date.isoformat() if reservation.start_date else None,
                        "end_date": reservation.end_date.isoformat() if reservation.end_date else None,
                        "status": reservation.status,
                        "contact_name": reservation.contact.name if hasattr(reservation, 'contact') and reservation.contact else None,
                    },
                    "message": f"To update this income, edit reservation '{reservation.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Check if the income is linked to a contract
            if income.contract_id is not None:
                contract = income.contract  # Already loaded via select_related
                
                return Response({
                    "error": f"Income '{income.title}' cannot be updated because it is linked to contract '{contract.title}'",
                    "can_update": False,
                    "contract": {
                        "id": str(contract.id),
                        "title": contract.title,
                        "start_date": contract.start_date.isoformat() if contract.start_date else None,
                        "end_date": contract.end_date.isoformat() if contract.end_date else None,
                        "status": contract.status,
                        "contact_name": contract.contact.name if hasattr(contract, 'contact') and contract.contact else None,
                    },
                    "message": f"To update this income, edit contract '{contract.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Income is not linked to a reservation or contract, proceed with update
            
            # Capture the previous state before making changes
            previous_data = income.to_dict()
            
            serializer = IncomeCreateSerializer(
                income,
                data=request.data,
                partial=True
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update with current user as updater
            income = serializer.save(updated_by=request.user)
            
            # Create history record with the previous state
            income.create_history_record(previous_data, request.user)
            
            # Create notification for the income update
            from notifications.utils import create_system_notification
            
            # Determine what was changed
            changes = []
            current_data = income.to_dict()
            
            # Check key fields for changes
            if previous_data['amount'] != current_data['amount']:
                changes.append(f"amount changed from {previous_data['amount']} to {current_data['amount']}")
            
            if previous_data['status'] != current_data['status']:
                changes.append(f"status changed from {previous_data['status']} to {current_data['status']}")
            
            if previous_data['due_date'] != current_data['due_date']:
                changes.append(f"due date changed")
            
            change_description = ", ".join(changes) if changes else "details updated"
            
            create_system_notification(
                title=f"Income '{income.title}' updated",
                title_ar=f"تم تحديث الدخل '{income.title}'",
                message=f"The income '{income.title}' was updated by {request.user.email}: {change_description}",
                message_ar=f"تم تحديث الدخل '{income.title}' بواسطة {request.user.email}: {change_description}",
                priority="low",  # Changed from medium to low
                notification_type_name="Income Update",
                category="income",
                category_id=str(income.id) if income.id else None,
            )
            
            # Return detailed income info
            detail_serializer = IncomeDetailSerializer(income)
            return Response(detail_serializer.data)
    except Income.DoesNotExist:
        return Response(
            {"error": f"Income with ID {income_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to update income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_income_status(request):
    """
    Update only the status of an income event
    Request body should include income_id and status
    Optionally include received_date when status is 'completed'
    """
    try:
        with transaction.atomic():
            # Get the income_id from request data
            income_id = request.data.get('income_id')
            if not income_id:
                return Response(
                    {"error": "income_id is required in the request body"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get the income
            try:
                income = get_object_or_404(Income, id=income_id)
            except (ValueError, TypeError):
                return Response(
                    {"error": f"Invalid income ID format: {income_id}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get the new status from request data
            new_status = request.data.get('status')
            if not new_status:
                return Response(
                    {"error": "Status is required"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Validate the status is a valid choice
            valid_statuses = [choice[0] for choice in Income.IncomeStatus.choices]
            if new_status not in valid_statuses:
                return Response(
                    {
                        "error": f"Invalid status value. Must be one of: {', '.join(valid_statuses)}"
                    }, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Capture the previous state before making changes
            previous_data = income.to_dict()
            
            # Update only the status
            old_status = income.status
            income.status = new_status
            
            # Handle received_date when status is completed
            if new_status == Income.IncomeStatus.COMPLETED:
                # Get received_date from request body, or use current time if not provided
                received_date_str = request.data.get('paid_date')
                if received_date_str:
                    try:
                        # Parse the provided received_date
                        from django.utils.dateparse import parse_datetime
                        parsed_received_date = parse_datetime(received_date_str)
                        if parsed_received_date:
                            income.received_date = parsed_received_date
                        else:
                            # If parsing fails, use current time
                            income.received_date = timezone.now()
                    except Exception:
                        # If any error in parsing, use current time
                        income.received_date = timezone.now()
                else:
                    # If no received_date provided and no existing received_date, set to now
                    if not income.received_date:
                        income.received_date = timezone.now()
            else:
                # Clear received_date if changing from completed to another status
                if old_status == Income.IncomeStatus.COMPLETED and new_status != Income.IncomeStatus.COMPLETED:
                    income.received_date = None
                
            # Update with current user as updater
            income.updated_by = request.user
            income.save(update_fields=['status', 'received_date', 'updated_by'])
            
            # Create history record with the previous state
            income.create_history_record(previous_data, request.user)
            
            # Create notification for the status update
            from notifications.utils import create_system_notification
            create_system_notification(
                title=f"Income Status Updated: {income.title}",
                title_ar=f"تم تحديث حالة الدخل: {income.title}",
                message=f"The income '{income.title}' status was changed from '{old_status}' to '{new_status}' by {request.user.email}",
                message_ar=f"تم تغيير حالة الدخل '{income.title}' من '{old_status}' إلى '{new_status}' بواسطة {request.user.email}",
                priority="low",
                notification_type_name="Income Status Update",
                category="income",
                category_id=str(income.id) if income.id else None,
            )
            
            # Return detailed income info
            detail_serializer = IncomeDetailSerializer(income)
            return Response(detail_serializer.data)
    except Income.DoesNotExist:
        return Response(
            {"error": f"Income with the provided ID not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to update income status", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_income(request, income_id):
    try:
        income = get_object_or_404(Income, id=income_id)
        
        # Check if we're doing a soft delete or permanent delete
        permanent = request.query_params.get('permanent', 'false').lower() == 'true'
        
        if permanent:
            # Get confirmation from request to prevent accidental deletion
            confirm = request.query_params.get('confirm', 'false').lower() == 'true'
            if not confirm:
                return Response(
                    {"warning": "This will permanently delete the income. Set confirm=true to proceed."}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Save income info for response
            income_title = income.title
            
            # Delete the income and all its history (cascade)
            income.delete()
            
            return Response({
                "message": f"Income '{income_title}' has been permanently deleted"
            }, status=status.HTTP_200_OK)
        else:
            # Perform soft delete
            income.soft_delete(request.user)
            
            return Response({
                "message": f"Income '{income.title}' has been marked as deleted"
            }, status=status.HTTP_200_OK)
    except Income.DoesNotExist:
        return Response(
            {"error": f"Income with ID {income_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_income_history(request, income_id):
    """Retrieve history records for a specific income"""
    try:
        # First check if income exists
        income = get_object_or_404(Income, id=income_id)
        
        # Get all history records for this income
        history_records = IncomeHistory.objects.filter(income=income).order_by('-modified_at')
        
        # Handle case of no history records
        if not history_records.exists():
            return Response({
                "income_id": income_id,
                "income_title": income.title,
                "history": [],
                "message": "No history records found for this income"
            })
        
        # Serialize the history records
        serializer = IncomeHistorySerializer(history_records, many=True)
        
        return Response({
            "income_id": income_id,
            "income_title": income.title,
            "history": serializer.data,
            "count": history_records.count()
        })
    except Income.DoesNotExist:
        return Response(
            {"error": f"Income with ID {income_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve income history", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_income_received(request, income_id):
    """Mark an income as received with the current date"""
    try:
        with transaction.atomic():
            income = get_object_or_404(Income, id=income_id)
            
            # Check if already received
            if income.received_date is not None:
                return Response(
                    {"error": "Income is already marked as received"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Check if cancelled
            if income.status == Income.IncomeStatus.CANCELLED:
                return Response(
                    {"error": "Cannot mark a cancelled income as received"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Capture the previous state before making changes
            previous_data = income.to_dict()
            
            # Mark as received and completed
            income.received_date = timezone.now()
            income.status = Income.IncomeStatus.COMPLETED
            income.updated_by = request.user
            income.save()
            
            # Create history record with the previous state
            income.create_history_record(previous_data, request.user)
            
            return Response({
                "message": f"Income '{income.title}' has been marked as received",
                "income": IncomeDetailSerializer(income).data
            })
    except Income.DoesNotExist:
        return Response(
            {"error": f"Income with ID {income_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to mark income as received", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_income_types(request):
    """List all income types"""
    try:
        income_types = IncomeType.objects.all()
        serializer = IncomeTypeSerializer(income_types, many=True)
        return Response({"income_types": serializer.data, "count": income_types.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve income types", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_income_type(request):
    """Create a new income type"""
    try:
        serializer = IncomeTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if type with same name already exists
        name = serializer.validated_data['name']
        if IncomeType.objects.filter(name__iexact=name).exists():
            return Response(
                {"error": f"Income type '{name}' already exists"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create new income type
        income_type = serializer.save(created_by=request.user)
        
        return Response(
            IncomeTypeSerializer(income_type).data,
            status=status.HTTP_201_CREATED
        )
    except Exception as e:
        return Response(
            {"error": "Failed to create income type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_income_type(request, type_id):
    """Delete an income type"""
    try:
        income_type = get_object_or_404(IncomeType, id=type_id)
        
        # Check if type is used by any incomes
        if Income.objects.filter(type=income_type).exists():
            return Response(
                {"error": f"Cannot delete income type '{income_type.name}' as it is being used by income records"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Store type name for response
        type_name = income_type.name
        
        # Delete the type
        income_type.delete()
        
        return Response({
            "message": f"Income type '{type_name}' has been deleted"
        }, status=status.HTTP_200_OK)
    except IncomeType.DoesNotExist:
        return Response(
            {"error": f"Income type with ID {type_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete income type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def list_incomes_by_location(request):
    """
    Retrieve all incomes filtered by a specific location ID.
    POST request with location_id in the body.
    """
    try:
        # Get location_id from request body
        location_id = request.data.get('location_id')
        if not location_id:
            return Response(
                {"error": "location_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Load all child incomes directly, avoiding subqueries
        all_child_incomes = {}
        
        # Get all parent events
        parent_events = ParentIncome.objects.filter(
            is_deleted=False
        ).select_related(
            'created_by', 'updated_by', 'type'
        ).order_by('-created_at')
        
        # Get IDs for further operations
        parent_ids = list(parent_events.values_list('id', flat=True))
        
        # Get all incomes in one query with location filter - IMPROVED PREFETCHING
        all_incomes = Income.objects.filter(
            (Q(parent__in=parent_ids, is_deleted=False) |  # Child incomes
            Q(parent__isnull=True, is_deleted=False)) &    # Normal incomes
            Q(location__id=location_id)                   # Filter by location
        ).select_related(
            'type', 'created_by', 'updated_by', 'parent', 'contact', 'location', 'reservation'
        ).prefetch_related(
            'location__ownership_shares',
            'location__ownership_shares__contact'
        )
        
        # Separate child incomes and normal incomes
        normal_incomes = []
        filtered_parent_ids = set()  # Track which parents have matching children
        
        for income in all_incomes:
            if income.parent_id:
                if income.parent_id not in all_child_incomes:
                    all_child_incomes[income.parent_id] = []
                all_child_incomes[income.parent_id].append(income)
                filtered_parent_ids.add(income.parent_id)
            else:
                normal_incomes.append(income)
        
        # Filter parent events to only include those with matching children
        parent_events = [parent for parent in parent_events if parent.id in filtered_parent_ids]
        
        # Get all income IDs for prefetching related data
        all_income_ids = [income.id for income in all_incomes]

        # Pre-process location ownership data for all incomes
        for income in all_incomes:
            if income.location:
                # Cache calculated values on the location object
                location = income.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage
        
        # Prefetch all history in one query with raw SQL
        from django.db import connection
        history_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT ih.income_id, ih.modified_at, ih.id AS history_id, u.id AS user_id, u.username, u.email, u.role
                FROM income_incomehistory ih
                LEFT JOIN users_user u ON ih.modified_by_id = u.id
                WHERE ih.income_id = ANY(%s)
                ORDER BY ih.modified_at DESC
            """, [all_income_ids])
            for row in cursor.fetchall():
                income_id, modified_at, history_id, user_id, username, user_email, user_role = row
                history_map.setdefault(income_id, []).append({
                    'id': history_id,
                    'modified_at': modified_at,
                    'modified_by': username,
                    'user': {
                        'id': user_id,
                        'username': username,
                        'email': user_email,
                        'role': user_role
                    } if user_id else None
                })
        
        # Attach the prefetched data to parent events
        for parent in parent_events:
            parent.prefetched_child_events = all_child_incomes.get(parent.id, [])
            # Find first child's due date for parent
            if parent.prefetched_child_events:
                parent.first_child_due_date = sorted(
                    parent.prefetched_child_events,
                    key=lambda x: x.due_date
                )[0].due_date
            else:
                parent.first_child_due_date = None
            
            # Attach prefetched data to each child income
            for child in parent.prefetched_child_events:
                child.prefetched_history = history_map.get(child.id, [])
        
        # Attach prefetched data to normal incomes
        for income in normal_incomes:
            income.prefetched_history = history_map.get(income.id, [])

        # Store original methods before patching
        from locations.models import Location
        original_are_we_owners = Location.are_we_owners
        original_get_our_percentage = Location.get_our_Percentage

        # Define patched methods
        def patched_are_we_owners(self):
            if hasattr(self, '_ownership_processed'):
                return self._has_primary_owner
            return original_are_we_owners(self)
        
        def patched_get_our_percentage(self):
            if hasattr(self, '_ownership_processed'):
                return self._our_percentage
            return original_get_our_percentage(self)
        
        # Apply patches
        Location.are_we_owners = patched_are_we_owners
        Location.get_our_Percentage = patched_get_our_percentage
        
        try:
            # Create serializers with the prefetched data
            context = {'prefetched_data': True}
            parent_serializer = ParentEventDetailSerializer(parent_events, many=True, context=context)
            normal_serializer = IncomeSimpleSerializer(normal_incomes, many=True, context=context)
            
            # Combine and sort by date
            combined_incomes = parent_serializer.data + normal_serializer.data
            combined_incomes.sort(key=lambda x: x.get('created_at', ''))
            
        finally:
            # Restore original methods
            Location.are_we_owners = original_are_we_owners
            Location.get_our_Percentage = original_get_our_percentage
        
        # Add location info to response
        from locations.models import Location
        try:
            location = Location.objects.get(id=location_id)
            location_info = {
                "id": location_id,
                "name": location.name,
                "address": location.address
            }
        except:
            location_info = {"id": location_id}
        
        return Response({
            "location": location_info,
            "incomes": combined_incomes, 
            "count": len(combined_incomes)
        }, status=status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve incomes by location", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def list_incomes_by_contact(request):
    """
    Retrieve all incomes filtered by a specific contact ID.
    POST request with contact_id in the body.
    """
    try:
        # Get contact_id from request body
        contact_id = request.data.get('contact_id')
        if not contact_id:
            return Response(
                {"error": "contact_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Load all child incomes directly, avoiding subqueries
        all_child_incomes = {}
        
        # Get all parent events
        parent_events = ParentIncome.objects.filter(
            is_deleted=False
        ).select_related(
            'created_by', 'updated_by', 'type'
        ).order_by('-created_at')
        
        # Get IDs for further operations
        parent_ids = list(parent_events.values_list('id', flat=True))
        
        # Get all incomes in one query with contact filter
        all_incomes = Income.objects.filter(
            (Q(parent__in=parent_ids, is_deleted=False) |  # Child incomes
            Q(parent__isnull=True, is_deleted=False)) &    # Normal incomes
            Q(contact__id=contact_id)                     # Filter by contact
        ).select_related(
            'type', 'created_by', 'updated_by', 'parent', 'reservation'
        ).distinct()  # Avoid duplicates if multiple contacts match
        
        # Separate child incomes and normal incomes
        normal_incomes = []
        filtered_parent_ids = set()  # Track which parents have matching children
        
        for income in all_incomes:
            if income.parent_id:
                if income.parent_id not in all_child_incomes:
                    all_child_incomes[income.parent_id] = []
                all_child_incomes[income.parent_id].append(income)
                filtered_parent_ids.add(income.parent_id)
            else:
                normal_incomes.append(income)
        
        # Filter parent events to only include those with matching children
        parent_events = [parent for parent in parent_events if parent.id in filtered_parent_ids]
        
        # Get all income IDs for prefetching related data
        all_income_ids = [income.id for income in all_incomes]
        
        # Prefetch all contacts in one query with raw SQL
        from django.db import connection
        contact_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT ic.id, c.id, c.name, c.email
                FROM income_income ic
                JOIN contacts_contact c ON ic.contact_id = c.id
                WHERE ic.id = ANY(%s)
            """, [all_income_ids])
            for row in cursor.fetchall():
                id, contact_id, contact_name, contact_email = row
                if id not in contact_map:
                    contact_map[id] = []
                contact_map[id].append({
                    'id': contact_id,
                    'name': contact_name,
                    'email': contact_email
                })
        
        # Prefetch all locations in one query with raw SQL
        location_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT il.id, l.id, l.name, l.address 
                FROM income_income il
                JOIN locations_location l ON il.location_id = l.id
                WHERE il.id = ANY(%s)
            """, [all_income_ids])
            for row in cursor.fetchall():
                id, location_id, location_name, location_address = row
                if id not in location_map:
                    location_map[id] = []
                location_map[id].append({
                    'id': location_id,
                    'name': location_name,
                    'address': location_address
                })
        
        # Prefetch all history in one query with raw SQL
        history_map = {}
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT ih.income_id, ih.modified_at, u.username
                FROM income_incomehistory ih
                LEFT JOIN users_user u ON ih.modified_by_id = u.id
                WHERE ih.income_id = ANY(%s)
                ORDER BY ih.modified_at DESC
            """, [all_income_ids])
            for row in cursor.fetchall():
                income_id, modified_at, username = row
                if income_id not in history_map:
                    history_map[income_id] = []
                history_map[income_id].append({
                    'modified_at': modified_at,
                    'modified_by': username
                })
        
        # Attach the prefetched data to parent events
        for parent in parent_events:
            parent.prefetched_child_events = all_child_incomes.get(parent.id, [])
            # Find first child's due date for parent
            if parent.prefetched_child_events:
                parent.first_child_due_date = sorted(
                    parent.prefetched_child_events,
                    key=lambda x: x.due_date
                )[0].due_date
            else:
                parent.first_child_due_date = None
            
            # Attach prefetched data to each child income
            for child in parent.prefetched_child_events:
                child.prefetched_contacts = contact_map.get(child.id, [])
                child.prefetched_locations = location_map.get(child.id, [])
                child.prefetched_history = history_map.get(child.id, [])
        
        # Attach prefetched data to normal incomes
        for income in normal_incomes:
            income.prefetched_contacts = contact_map.get(income.id, [])
            income.prefetched_locations = location_map.get(income.id, [])
            income.prefetched_history = history_map.get(income.id, [])
        
        # Create serializers with the prefetched data
        context = {'prefetched_data': True}
        parent_serializer = ParentEventDetailSerializer(parent_events, many=True, context=context)
        normal_serializer = IncomeSimpleSerializer(normal_incomes, many=True, context=context)
        
        # Combine and sort by date
        combined_incomes = parent_serializer.data + normal_serializer.data
        combined_incomes.sort(key=lambda x: x.get('created_at', ''))
        
        # Add contact info to response
        from contacts.models.contacts import Contact
        try:
            contact = Contact.objects.get(id=contact_id)
            contact_info = {
                "id": contact_id,
                "name": contact.name,
                "email": contact.email
            }
        except:
            contact_info = {"id": contact_id}
        
        return Response({
            "contact": contact_info,
            "incomes": combined_incomes, 
            "count": len(combined_incomes)
        }, status=status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve incomes by contact", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def soft_delete_income(request, income_id):
    """Soft delete an income by setting is_deleted to True, but only if not linked to a reservation or contract"""
    try:
        with transaction.atomic():  # Wrap in transaction to ensure all or nothing
            # Get the income with proper UUID conversion
            try:
                income_uuid = uuid.UUID(str(income_id))
                income = Income.objects.select_related('reservation', 'contract', 'contact').get(id=income_uuid)
            except (ValueError, TypeError):
                return Response(
                    {"error": f"Invalid income ID format: {income_id}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if income is already deleted
            if income.is_deleted:
                return Response(
                    {"warning": f"Income '{income.title}' is already marked as deleted"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if the income is linked to a reservation
            if income.reservation_id is not None:
                # Return information about the associated reservation without deleting the income
                reservation = income.reservation  # Already loaded via select_related
                
                return Response({
                    "warning": f"Income '{income.title}' was not deleted because it is linked to reservation '{reservation.title}'",
                    "deleted": False,
                    "reservation": {
                        "id": str(reservation.id),
                        "title": reservation.title,
                        "start_date": reservation.start_date.isoformat() if reservation.start_date else None,
                        "end_date": reservation.end_date.isoformat() if reservation.end_date else None,
                        "status": reservation.status,
                        "contact_name": reservation.contact.name if hasattr(reservation, 'contact') and reservation.contact else None,
                    },
                    "message": f"To delete this income, edit reservation '{reservation.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Check if the income is linked to a contract
            if income.contract_id is not None:
                # Return information about the associated contract without deleting the income
                contract = income.contract  # Already loaded via select_related
                
                return Response({
                    "warning": f"Income '{income.title}' was not deleted because it is linked to contract '{contract.title}'",
                    "deleted": False,
                    "contract": {
                        "id": str(contract.id),
                        "title": contract.title,
                        "start_date": contract.start_date.isoformat() if contract.start_date else None,
                        "end_date": contract.end_date.isoformat() if contract.end_date else None,
                        "status": contract.status,
                        "contact_name": contract.contact.name if hasattr(contract, 'contact') and contract.contact else None,
                    },
                    "message": f"To delete this income, edit contract '{contract.title}' first"
                }, status=status.HTTP_409_CONFLICT)
            
            # Income is not linked to a reservation or contract, proceed with soft deletion
            
            # Capture the previous state before making changes
            previous_data = income.to_dict()
            
            # Set is_deleted=True
            income.is_deleted = True
            income.save(update_fields=['is_deleted'])
            
            # Create history record with the previous state
            income.create_history_record(previous_data, request.user)
            
            # Delete old notifications for this income before creating new ones
            delete_notifications_by_category("income", str(income.id))
            
            # Create notification for the income deletion
            create_system_notification(
                title=f"Income '{income.title}' Deleted",
                title_ar=f"تم حذف الدخل '{income.title}'",
                message=f"The income '{income.title}' was soft-deleted by {request.user.email}",
                message_ar=f"تم حذف الدخل '{income.title}' بواسطة {request.user.email}",
                priority="medium", 
                notification_type_name="Income Deletion",
                category="income",
                category_id=str(income.id) if income.id else None,
            )
            
            return Response({
                "message": f"Income '{income.title}' has been marked as deleted",
                "deleted": True
            }, status=status.HTTP_200_OK)
    except Income.DoesNotExist:
        return Response(
            {"error": f"Income with ID {income_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to delete income", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# Internal function for direct calling from other modules
def get_income_by_reservation_internal(reservation_id):
    """
    Internal function to retrieve all incomes associated with a specific reservation.
    
    Args:
        reservation_id: UUID of the reservation
        
    Returns:
        Dictionary with incomes data, count, and total
    """
    try:
        # Query for incomes with this reservation ID
        incomes = Income.objects.filter(
            reservation_id=reservation_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for income in incomes:
            if income.location:
                # Cache calculated values on the location object
                location = income.location
                if not hasattr(location, '_ownership_processed'):
                    # Use prefetched relationship to avoid additional queries
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    # Store the calculated values
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()
        
        # Serialize the data
        serializer = IncomeSimpleSerializer(incomes, many=True, context={'prefetched_data': True})
        data = serializer.data
        
        # Calculate the total amount
        total_amount = sum(income.amount for income in incomes)
        
        # Unpatch methods
        LocationPatch.unpatch(originals)
        
        # Return data
        return {
            "incomes": data,
            "count": len(data),
            "total": float(total_amount)
        }
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return {
            "incomes": [],
            "count": 0,
            "total": 0,
            "error": str(e)
        }

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_income_by_reservation(request):
    """
    API endpoint to retrieve all incomes associated with a specific reservation.
    
    Expects a POST request with reservation_id in the body.
    Returns a structured response with income data.
    """
    try:
        # Get reservation_id from request body
        reservation_id = request.data.get('reservation_id')
        if not reservation_id:
            return Response(
                {"error": "reservation_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Use the internal function to get data
        result = get_income_by_reservation_internal(reservation_id)
        
        # Check for errors
        if "error" in result and not result["incomes"]:
            return Response(
                {"error": "Failed to retrieve incomes", "details": result["error"]},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Return response
        return Response(result)
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve incomes for reservation", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# Internal function for direct calling from other modules
def get_income_by_contract_internal(contract_id):
    """
    Internal function to retrieve all incomes associated with a specific contract.
    
    Args:
        contract_id: UUID of the contract
        
    Returns:
        Dictionary with incomes data, count, and total
    """
    try:
        # Query for incomes with this contract ID
        incomes = Income.objects.filter(
            contract_id=contract_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for income in incomes:
            if income.location:
                # Cache calculated values on the location object
                location = income.location
                if not hasattr(location, '_ownership_processed'):
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                # Store original methods
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                # Define patched methods
                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                # Apply patches
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                # Restore original methods
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        # Patch methods, serialize, then unpatch
        originals = LocationPatch.patch()
        
        # Serialize the data
        serializer = IncomeSimpleSerializer(incomes, many=True, context={'prefetched_data': True})
        data = serializer.data
        
        # Calculate the total amount
        total_amount = sum(income.amount for income in incomes)
        
        # Unpatch methods
        LocationPatch.unpatch(originals)
        
        # Return data
        return {
            "incomes": data,
            "count": len(data),
            "total": float(total_amount)
        }
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return {
            "incomes": [],
            "count": 0,
            "total": 0,
            "error": str(e)
        }

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_income_by_contract(request):
    """
    API endpoint to retrieve all incomes associated with a specific contract.
    
    Expects a POST request with contract_id in the body.
    Returns a structured response with income data.
    """
    try:
        # Get contract_id from request body
        contract_id = request.data.get('contract_id')
        if not contract_id:
            return Response(
                {"error": "contract_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Use the internal function to get data
        result = get_income_by_contract_internal(contract_id)
        
        # Check for errors
        if "error" in result and not result["incomes"]:
            return Response(
                {"error": "Failed to retrieve incomes", "details": result["error"]},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        
        # Return response
        return Response(result)
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve incomes for contract", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def get_income_by_contact_internal(contact_id):
    """
    Internal function to retrieve all incomes associated with a specific contact.
    
    Args:
        contact_id: UUID of the contact
        
    Returns:
        Dictionary with incomes data, count, and total
    """
    try:
        # Query for incomes with this contact ID
        incomes = Income.objects.filter(
            contact_id=contact_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for income in incomes:
            if income.location:
                # Cache calculated values on the location object
                location = income.location
                if not hasattr(location, '_ownership_processed'):
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        originals = LocationPatch.patch()
        serializer = IncomeSimpleSerializer(incomes, many=True, context={'prefetched_data': True})
        data = serializer.data
        total_amount = sum(income.amount for income in incomes)
        LocationPatch.unpatch(originals)

        return {
            "incomes": data,
            "count": len(data),
            "total": float(total_amount)
        }
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return {
            "incomes": [],
            "count": 0,
            "total": 0,
            "error": str(e)
        }

def get_income_by_location_internal(location_id):
    """
    Internal function to retrieve all incomes associated with a specific location.
    
    Args:
        location_id: UUID of the location
        
    Returns:
        Dictionary with incomes data, count, and total
    """
    try:
        # Query for incomes with this location ID
        incomes = Income.objects.filter(
            location_id=location_id,
            is_deleted=False
        ).select_related(
            'type', 'contact', 'location', 'created_by', 'updated_by'
        ).prefetch_related(
            'location__ownership_shares', 
            'location__ownership_shares__contact'
        )
        
        # Pre-process location ownership data
        for income in incomes:
            if income.location:
                # Cache calculated values on the location object
                location = income.location
                if not hasattr(location, '_ownership_processed'):
                    has_primary_owner = False
                    our_percentage = 0
                    
                    for share in location.ownership_shares.all():
                        if share.our_company:
                            our_percentage = float(share.percentage)
                            if share.is_primary_owner:
                                has_primary_owner = True
                    
                    location._ownership_processed = True
                    location._has_primary_owner = has_primary_owner
                    location._our_percentage = our_percentage

        # Create a class to patch Location methods
        class LocationPatch:
            @staticmethod
            def patch():
                original_are_we_owners = Location.are_we_owners
                original_get_our_percentage = Location.get_our_Percentage

                def patched_are_we_owners(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._has_primary_owner
                    return original_are_we_owners(self)
                
                def patched_get_our_percentage(self):
                    if hasattr(self, '_ownership_processed'):
                        return self._our_percentage
                    return original_get_our_percentage(self)
                
                Location.are_we_owners = patched_are_we_owners
                Location.get_our_Percentage = patched_get_our_percentage
                
                return {
                    'original_are_we_owners': original_are_we_owners, 
                    'original_get_our_percentage': original_get_our_percentage
                }
            
            @staticmethod
            def unpatch(originals):
                Location.are_we_owners = originals['original_are_we_owners']
                Location.get_our_Percentage = originals['original_get_our_percentage']

        originals = LocationPatch.patch()
        serializer = IncomeSimpleSerializer(incomes, many=True, context={'prefetched_data': True})
        data = serializer.data
        total_amount = sum(income.amount for income in incomes)
        LocationPatch.unpatch(originals)

        return {
            "incomes": data,
            "count": len(data),
            "total": float(total_amount)
        }
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return {
            "incomes": [],
            "count": 0,
            "total": 0,
            "error": str(e)
        }