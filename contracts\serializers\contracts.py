from rest_framework import serializers
from django.utils import timezone
from contracts.models.contracts import Contract, ContractHistory, RenewalTerms , ContractDocument
from contacts.serializers.contacts import ContactSimpleSerializer
from locations.serializers.locations import LocationSimpleSerializer , LocationMinimalSerializer
from users.serializers.users import UserDetailSerializer
from contacts.models.contacts import Contact
from locations.models.locations import Location

class RenewalTermsSerializer(serializers.ModelSerializer):
    class Meta:
        model = RenewalTerms
        fields = ['auto_renew', 'increase_percentage', 'notice_period_days']


class ContractHistorySerializer(serializers.ModelSerializer):
    modified_by = UserDetailSerializer(read_only=True)
    
    class Meta:
        model = ContractHistory
        fields = ['id', 'modified_at', 'modified_by', 'data']
        read_only_fields = fields

class ContractDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContractDocument
        fields = '__all__'

class ContractCreateSerializer(serializers.ModelSerializer):
    contact_id = serializers.UUIDField(required=True)
    location_id = serializers.UUIDField(required=True)
    renewal_terms = RenewalTermsSerializer(required=False)
    
    class Meta:
        model = Contract
        fields = [
            'title', 'description', 'status', 'start_date', 'end_date',
            'contact_id', 'location_id', 'total_amount', 'renewal_terms', 'notes'
        ]
        extra_kwargs = {
            'title': {'required': True},
            'start_date': {'required': True},
            'end_date': {'required': True},
            'total_amount': {'required': True}
        }
    
    def validate(self, data):
        """Validate contract data"""
        # Extract IDs from nested dicts if necessary
        contact = data.get('contact_id')
        if isinstance(contact, dict):
            contact = contact.get('id')
        data['contact_id'] = contact

        location = data.get('location_id')
        if isinstance(location, dict):
            location = location.get('id')
        data['location_id'] = location

        # Validate contact
        try:
            if contact:
                contact_obj = Contact.objects.get(pk=contact)
                if contact_obj.is_deleted:
                    raise serializers.ValidationError({"contact_id": "This contact has been deleted"})
        except Contact.DoesNotExist:
            raise serializers.ValidationError({"contact_id": "Contact does not exist"})

        # Validate location
        try:
            if location:
                location_obj = Location.objects.get(pk=location)
                if location_obj.is_deleted:
                    raise serializers.ValidationError({"location_id": "This location has been deleted"})
        except Location.DoesNotExist:
            raise serializers.ValidationError({"location_id": "Location does not exist"})

        # Validate contract dates
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        if start_date and end_date and end_date <= start_date:
            raise serializers.ValidationError({"end_date": "End date must be after start date"})

        return data
        
    def create(self, validated_data):
        renewal_terms_data = validated_data.pop('renewal_terms', None)
        documents_data = validated_data.pop('documents', [])
        
        # Create contract first (removed the mapping code)
        contract = Contract.objects.create(**validated_data)
        
        # Create renewal terms if provided
        if renewal_terms_data:
            renewal_terms = RenewalTerms.objects.create(**renewal_terms_data)
            contract.renewal_terms = renewal_terms
            contract.save(update_fields=['renewal_terms'])
        
        return contract
    
    def update(self, instance, validated_data):
        renewal_terms_data = validated_data.pop('renewal_terms', None)
        documents_data = validated_data.pop('documents', None)
        
        # Removed the mapping code
        
        # Update renewal terms if provided
        if renewal_terms_data:
            if instance.renewal_terms:
                # Update existing renewal terms
                for key, value in renewal_terms_data.items():
                    setattr(instance.renewal_terms, key, value)
                instance.renewal_terms.save()
            else:
                # Create new renewal terms
                renewal_terms = RenewalTerms.objects.create(**renewal_terms_data)
                instance.renewal_terms = renewal_terms
        

        
        # Update other fields
        for field, value in validated_data.items():
            setattr(instance, field, value)
        
        instance.save()
        return instance

class ContractSimpleSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    contact_name = serializers.CharField(source='contact.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    actual_amount = serializers.SerializerMethodField()

    class Meta:
        model = Contract
        fields = [
            'id', 'title', 'start_date', 'end_date', 'actual_amount',
            'status', 'status_display', 'contact_name', 
            'location_name', 'total_amount', 'is_deleted'
        ]
        read_only_fields = fields
    
    def get_actual_amount(self, obj):
        if obj.location and not obj.location.are_we_owners():
            from decimal import Decimal
            return str(obj.total_amount * Decimal(obj.location.get_our_Percentage()))
        return str(obj.total_amount)
    

class ContractListResponseSerializer(serializers.ModelSerializer):
    contact = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    contractNumber = serializers.SerializerMethodField()
    actual_amount = serializers.SerializerMethodField()
    document_uploaded = serializers.SerializerMethodField()
    renewal_terms = serializers.SerializerMethodField()
    # Add client field for backward compatibility
    client = serializers.SerializerMethodField()
    
    class Meta:
        model = Contract
        fields = [
            'id', 'title', 'contractNumber', 'description', 'status', 'actual_amount',
            'start_date', 'end_date', 'contact', 'location', 'client',
            'total_amount', 'document_uploaded', 'renewal_terms', 'created_at', 'updated_at', 'notes'
        ]

    def get_contractNumber(self, obj):
        return f"CTR-{str(obj.id)[:8]}"

    def get_document_uploaded(self, obj):
        return [
            {
                "id": str(doc.id),
                "name": doc.name,
                "type": doc.type,
                "fileType": doc.file_type,
                "url": doc.url or (doc.file.url if doc.file else None)
            }
            for doc in obj.documents.all()
        ]
    
    def get_actual_amount(self, obj):
        if obj.location and not obj.location.are_we_owners():
            from decimal import Decimal
            return str(obj.total_amount * Decimal(str(obj.location.get_our_Percentage() / 100)))
        return str(obj.total_amount)

    def get_renewal_terms(self, obj):
        if obj.renewal_terms:
            return {
                'auto_renew': obj.renewal_terms.auto_renew,
                'increase_percentage': float(obj.renewal_terms.increase_percentage) if obj.renewal_terms.increase_percentage else None,
                'notice_period_days': obj.renewal_terms.notice_period_days
            }
        return None

    def get_contact(self, obj):
        # Check for pre-fetched contact types (from raw SQL)
        if hasattr(obj.contact, '_contact_types_list'):
            contact_types = obj.contact._contact_types_list
        # Use prefetched contact types if available
        elif hasattr(obj.contact, '_prefetched_types'):
            contact_types = [t.name for t in obj.contact._prefetched_types]
        else:
            # Fallback - this should ideally not happen if prefetch is set up correctly
            contact_types = [t.name for t in obj.contact.types.all()]
            
        return {
            'id': str(obj.contact.id),
            'name': obj.contact.name,
            'email': obj.contact.email,
            'types': contact_types
        }

    def get_client(self, obj):
        # Return the same data as contact for backward compatibility
        return self.get_contact(obj)

    def get_location(self, obj):
        # Use prefetched location data if available
        if hasattr(obj.location, '_prefetched_ownership_shares'):
            our_percentage = next(
                (float(share.percentage) for share in obj.location._prefetched_ownership_shares if share.our_company), 
                0.0
            )
        else:
            # Fallback
            our_percentage = obj.location.get_our_Percentage()
        
        return {
            'id': str(obj.location.id),
            'name': obj.location.name,
            'address': obj.location.address,
            'our_percentage': our_percentage
        }

class ContractDetailSerializer(serializers.ModelSerializer):
    contact = ContactSimpleSerializer(read_only=True)
    location = LocationSimpleSerializer(read_only=True)
    created_by = UserDetailSerializer(read_only=True)
    updated_by = UserDetailSerializer(read_only=True)
    history = ContractHistorySerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    renewal_terms = RenewalTermsSerializer(read_only=True)
    actual_amount = serializers.SerializerMethodField()
    class Meta:
        model = Contract
        fields = [
            'id', 'title', 'description', 'actual_amount',
            'start_date', 'end_date', 'status', 'status_display',
            'contact', 'location', 'total_amount', 'renewal_terms',
            'documents', 'notes', 'created_at', 'updated_at',
            'created_by', 'updated_by', 'is_deleted', 'history'
        ]
        read_only_fields = fields

    def get_actual_amount(self, obj):
        if obj.location and not obj.location.are_we_owners():
            from decimal import Decimal
            return str(obj.total_amount * Decimal(obj.location.get_our_Percentage()))
        return str(obj.total_amount)

    def to_representation(self, instance):
        """Override to use prefetched data efficiently"""
        # Check for pre-fetched contact types (from raw SQL)
        if hasattr(instance.contact, '_contact_types_list'):
            contact_types = instance.contact._contact_types_list
        # Use prefetched contact types if available
        elif hasattr(instance.contact, '_prefetched_types'):
            contact_types = [t.name for t in instance.contact._prefetched_types]
        else:
            # Fallback - this should ideally not happen if prefetch is set up correctly
            contact_types = [t.name for t in instance.contact.types.all()]
            
        # Use prefetched location data if available
        if hasattr(instance.location, '_prefetched_ownership_shares'):
            our_percentage = next(
                (float(share.percentage) for share in instance.location._prefetched_ownership_shares if share.our_company), 
                0.0
            )
        else:
            # Fallback
            our_percentage = instance.location.get_our_Percentage()
        
        return {
            'id': str(instance.id),
            'title': instance.title,
            'description': instance.description,
            'status': instance.status,
            'start_date': instance.start_date,
            'end_date': instance.end_date,
            'total_amount': float(instance.total_amount),
            'notes': instance.notes,
            'contact': {
                'id': str(instance.contact.id),
                'name': instance.contact.name,
                'email': instance.contact.email,
                'types': contact_types
            },
            'location': {
                'id': str(instance.location.id),
                'name': instance.location.name,
                'address': instance.location.address,
                'our_percentage': our_percentage
            },
            'created_at': instance.created_at,
            'updated_at': instance.updated_at,
            'created_by': instance.created_by.email if instance.created_by else None,
            'updated_by': instance.updated_by.email if instance.updated_by else None,
            'is_deleted': instance.is_deleted
        }