from django.urls import path
from notifications.api.notifications import (
    create_notification,
    list_notifications,
    get_notification,
    mark_as_read,
    mark_as_unread,
    list_notification_types,
    create_notification_type,
    delete_notification_type,
    check_due_items,
    get_unread_count
)

urlpatterns = [
    # Existing notification endpoints
    path('getall/', list_notifications, name='list_notifications'),
    path('create/', create_notification, name='create_notification'),
    path('getnotification/<uuid:notification_id>/', get_notification, name='get_notification'),
    path('markasread/<uuid:notification_id>/', mark_as_read, name='mark_as_read'),
    path('markasunread/<uuid:notification_id>/', mark_as_unread, name='mark_as_unread'),
    
    # New endpoints for notification types
    path('types/', list_notification_types, name='list_notification_types'),
    path('types/create/', create_notification_type, name='create_notification_type'),
    path('types/delete/<uuid:type_id>/', delete_notification_type, name='delete_notification_type'),
    
    # New endpoint for checking due items
    path('check-due-items/', check_due_items, name='check_due_items'),

    # New endpoint for getting unread count
    path('unread-count/', get_unread_count, name='get_unread_count'),
]