#!/usr/bin/env python
"""
Test script to debug login functionality
Run this script to test login and see detailed logs
"""

import os
import sys
import django
import requests
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sarayVera.settings')
django.setup()

from users.models.users import User
from django.contrib.auth import authenticate
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_user_creation():
    """Test creating a user"""
    try:
        logger.info("Testing user creation...")
        
        # Check if test user exists
        test_email = "<EMAIL>"
        if User.objects.filter(email=test_email).exists():
            logger.info(f"Test user {test_email} already exists")
            user = User.objects.get(email=test_email)
        else:
            logger.info(f"Creating test user {test_email}")
            user = User.objects.create_user(
                email=test_email,
                password="testpassword123",
                role=User.Role.ADMIN
            )
            logger.info(f"Test user created: {user}")
        
        return user
    except Exception as e:
        logger.error(f"Error creating test user: {str(e)}")
        return None

def test_authentication():
    """Test Django's authenticate function"""
    try:
        logger.info("Testing Django authentication...")
        
        test_email = "<EMAIL>"
        test_password = "testpassword123"
        
        user = authenticate(email=test_email, password=test_password)
        logger.info(f"Authentication result: {user}")
        
        if user:
            logger.info(f"Authentication successful for: {user.email}, role: {user.role}")
            return True
        else:
            logger.error("Authentication failed")
            return False
            
    except Exception as e:
        logger.error(f"Error during authentication: {str(e)}")
        return False

def test_api_login():
    """Test the login API endpoint"""
    try:
        logger.info("Testing login API endpoint...")
        
        url = "http://localhost:8000/users/api/login/"
        data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        logger.info(f"Making POST request to {url}")
        logger.info(f"Request data: {data}")
        
        response = requests.post(url, json=data)
        
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
        except:
            logger.info(f"Response text: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        logger.error(f"Error testing API login: {str(e)}")
        return False

def main():
    """Main test function"""
    logger.info("Starting login debugging tests...")
    
    # Test 1: User creation
    user = test_user_creation()
    if not user:
        logger.error("Failed to create/get test user. Stopping tests.")
        return
    
    # Test 2: Django authentication
    auth_success = test_authentication()
    if not auth_success:
        logger.error("Django authentication failed. Check user credentials.")
        return
    
    # Test 3: API login
    api_success = test_api_login()
    if api_success:
        logger.info("All tests passed!")
    else:
        logger.error("API login test failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
