from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from ..models.permissions_base import UserAccess
from ..serializers.permissions import UserPermissionsSerializer
from users.models import User
from ..decorators.permDecorators import check_permission

@api_view(['GET'])
@check_permission('users', 'view')
def get_user_permissions(request, user_id):
    """
    Get all permissions for a specific user
    """
    user = get_object_or_404(User, pk=user_id)
    user_access = get_object_or_404(UserAccess, user=user)
    serializer = UserPermissionsSerializer(user_access)
    return Response(serializer.data)

@api_view(['PUT'])
# @check_permission('users', 'edit')
def update_user_permissions(request, user_id):
    """
    Update all permissions for a specific user (full update)
    """
    user = get_object_or_404(User, pk=user_id)
    user_access = get_object_or_404(UserAccess, user=user)
    
    serializer = UserPermissionsSerializer(user_access, data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    serializer.save()
    return Response(serializer.data)


@api_view(['GET'])
@check_permission('users', 'view')
def list_all_users_permissions(request):
    """
    List permissions for all users (admin only)
    """
    users = User.objects.all()
    data = []
    
    for user in users:
        user_access = UserAccess.objects.filter(user=user).first()
        if user_access:
            serializer = UserPermissionsSerializer(user_access)
            data.append({
                'user_id': user.id,
                'email': user.email,
                'role': user.role,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'last_active': user.last_activity,
                'created_at': user.created_at,
                'username': user.username,
                'permissions': serializer.data
            })
    
    return Response(data)