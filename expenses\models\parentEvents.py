import uuid
from django.db import models
from django.utils import timezone
from users.models.users import User
from expenses.models.expensesEvents import EventType

class ParentEvent(models.Model):
    """Model for parent expense events that can have multiple child events"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    description = models.TextField(blank=True, null=True)
    
    # Type relationship
    type = models.ForeignKey(
        EventType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='parent_events'
    )
    
    # Tracking fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_parent_events'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='updated_parent_events'
    )
    
    # Soft delete field (ensuring this exists)
    is_deleted = models.BooleanField(default=False)
    
    def __str__(self):
        return f"{self.title} - {self.amount} (Parent)"
    
    def child_events_count(self):
        """Return the number of child events"""
        from expenses.models.expensesEvents import Event
        return Event.objects.filter(
            parent=self,
            is_deleted=False
        ).count()
    
    def child_events_total(self):
        """Return the total amount of child events"""
        from expenses.models.expensesEvents import Event
        return Event.objects.filter(
            parent=self,
            is_deleted=False
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or 0
    
    def to_dict(self):
        """Convert parent event to dictionary"""
        return {
            'title': self.title,
            'amount': float(self.amount),
            'description': self.description,
            'type_id': str(self.type.id) if self.type else None,
            'type_name': self.type.name if self.type else None,
        }
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['is_deleted']),
        ]
