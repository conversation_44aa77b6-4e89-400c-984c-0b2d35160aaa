from rest_framework import serializers
from expenses.models.parentEvents import ParentEvent
from expenses.models.expensesEvents import EventType, Event
from expenses.serializers.expenses import EventSimpleSerializer
from users.serializers.users import UserDetailSerializer

class ParentEventCreateSerializer(serializers.ModelSerializer):
    type_id = serializers.UUIDField(required=False, allow_null=True)
    
    class Meta:
        model = ParentEvent
        fields = ['title', 'amount', 'description', 'type_id']
        extra_kwargs = {
            'title': {'required': True},
            'amount': {'required': True}
        }
    
    def validate(self, data):
        # Validate type exists if provided
        type_id = data.pop('type_id', None)
        if type_id:
            try:
                event_type = EventType.objects.get(pk=type_id)
                data['type'] = event_type
            except EventType.DoesNotExist:
                raise serializers.ValidationError({"type_id": "Event type does not exist"})
        
        return data

class ParentEventSimpleSerializer(serializers.ModelSerializer):
    type_name = serializers.CharField(source='type.name', read_only=True)
    child_count = serializers.SerializerMethodField()
    child_events = EventSimpleSerializer(source='child_events.all', many=True, read_only=True)  # Add child events

    class Meta:
        model = ParentEvent
        fields = [
            'id', 'title', 'amount', 
            'type_name', 'created_at', 'child_count',
            'is_deleted', 'child_events'  # Include child_events in the fields
        ]
        read_only_fields = fields

    def get_child_count(self, obj):
        return obj.child_events_count()

class ParentEventDetailSerializer(serializers.ModelSerializer):
    created_by = UserDetailSerializer(read_only=True)
    updated_by = UserDetailSerializer(read_only=True)
    type = serializers.CharField(source='type.name', read_only=True)
    child_events = serializers.SerializerMethodField()
    child_count = serializers.SerializerMethodField()
    child_total = serializers.SerializerMethodField()
    dueDate = serializers.SerializerMethodField()
    category = serializers.CharField(default='expense', read_only=True)
    
    class Meta:
        model = ParentEvent
        fields = [
            'id', 'title', 'amount', 'description',
            'type', 'created_at', 'updated_at',
            'created_by', 'updated_by', 'is_deleted',
            'child_events', 'child_count', 'child_total', 'dueDate', 'category' 
        ]
        read_only_fields = fields
    
    def get_child_events(self, obj):
        # Use the prefetched child events
        if hasattr(obj, 'prefetched_child_events'):
            return EventSimpleSerializer(obj.prefetched_child_events, many=True, context=self.context).data
        return []
    
    def get_child_count(self, obj):
        # Use prefetched child events for counting
        if hasattr(obj, 'prefetched_child_events'):
            return len(obj.prefetched_child_events)
        return 0
    
    def get_child_total(self, obj):
        # Calculate from prefetched child events
        if hasattr(obj, 'prefetched_child_events'):
            return float(sum(event.amount for event in obj.prefetched_child_events))
        return 0.0
    
    def get_dueDate(self, obj):
        # Use the directly attached due date from first child
        if hasattr(obj, 'first_child_due_date'):
            return obj.first_child_due_date
        return None