from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status  # Make sure this import is present
from django.shortcuts import get_object_or_404
from django.core.exceptions import ValidationError
from django.db.models import Q
from django.db import transaction
from django.db.models import Prefetch
from django.db.models import Count
from reservations.models.reservations import Reservation
from contacts.models.contacts import OurCompany, Contact

from locations.models.locations import Location, LocationType, PartnerShare, LocationHistory
from locations.serializers.locations import (
    LocationCreateSerializer,
    LocationSimpleSerializer,
    LocationHistorySerializer,
    LocationTypeSerializer,
)
from sarayVera.settings import numOfQueriesWraper
from notifications.utils import create_system_notification, delete_notifications_by_category

# locations/api/locations.py
@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def create_location(request):
    try:
        with transaction.atomic():
            # Validate the input data
            serializer = LocationCreateSerializer(data=request.data, context={'request': request})
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if location with same name already exists and isn't deleted
            name = serializer.validated_data['name']
            if Location.objects.filter(name=name, is_deleted=False).exists():
                return Response(
                    {"error": "A location with this name already exists"}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Pre-fetch OurCompany instance to avoid duplicate queries
            company = OurCompany.get_instance()
            
            # Pre-fetch all Contact objects needed for ownership shares in a single query
            contact_ids = []
            if 'ownership_shares' in serializer.validated_data:
                for share in serializer.validated_data['ownership_shares']:
                    contact_ids.append(share.get('contact_id'))
                
            # Include company contact ID if it exists
            if company:
                contact_ids.append(company.id)
                
            # Fetch all contacts in a single query
            contacts_map = {}
            if contact_ids:
                contacts = Contact.objects.filter(id__in=contact_ids)
                contacts_map = {str(c.id): c for c in contacts}
            
            # Fetch location types in a single query if needed
            type_ids = getattr(serializer, 'validated_type_ids', [])
            location_types = None
            if type_ids:
                location_types = LocationType.objects.filter(id__in=type_ids)
                
            # Save the location with the current user
            location = serializer.save(
                created_by=request.user,
                updated_by=request.user,
                contacts_map=contacts_map,  # Pass the contacts map to avoid duplicate queries
                company=company,  # Pass the company instance to avoid duplicate queries
                location_types=location_types  # Pass pre-fetched location types
            )
            
            # Create notification for new location
            create_system_notification(
                title="New Location Added",
                title_ar=f"تم إضافة موقع جديد: {location.name}",
                message=f"A new location '{location.name}' was created by {request.user.email}",
                message_ar=f"تم إضافة موقع جديد '{location.name}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Location Created",
                category="location",
                category_id=str(location.id) if location.id else None
            )
            
        # Return location details with optimized query - fetch everything in one go
        returnLocation = Location.objects.select_related(
            'created_by', 'updated_by'
        ).prefetch_related(
            'types',
            Prefetch(
                'ownership_shares', 
                queryset=PartnerShare.objects.select_related('contact'),
                to_attr='ownership_shares_list'
            ),
            Prefetch(
                'reservations',
                queryset=Reservation.objects.filter(
                    is_deleted=False
                ).order_by('-start_date').select_related('contact'),
                to_attr='reservations_list'
            )
        ).get(id=location.id)
        
        detail_serializer = LocationSimpleSerializer(returnLocation, many=False)
        return Response(detail_serializer.data, status=status.HTTP_201_CREATED)
    except Exception as e:
        print(e)
        print(e.__traceback__.tb_lineno)
        return Response(
            {"error": "Failed to create location", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@numOfQueriesWraper
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_locations(request):
    try:
        # Filter out soft-deleted locations by default
        include_deleted = request.query_params.get('include_deleted', 'false').lower() == 'true'
        
        if include_deleted:
            locations = Location.objects.all().order_by('-updated_at')
        else:
            locations = Location.objects.filter(is_deleted=False).order_by('-updated_at')
        
        # Apply all filters BEFORE expensive operations
        # Filter by type if provided
        location_type = request.query_params.get('type')
        if location_type:
            locations = locations.filter(types__name=location_type)
            
        # Filter by primary owner
        primary_owner_id = request.query_params.get('primary_owner_id')
        if primary_owner_id:
            locations = locations.filter(ownership_shares__contact_id=primary_owner_id, ownership_shares__is_primary_owner=True)
            
        # Filter by active status
        active_only = request.query_params.get('active_only', 'false').lower() == 'true'
        if active_only:
            locations = locations.filter(is_active=True)
        
        # Filter by reserved status
        reserved = request.query_params.get('reserved')
        if reserved is not None:
            is_reserved = reserved.lower() == 'true'
            locations = locations.filter(is_reserved=is_reserved)
            
        # Search by name or address
        search = request.query_params.get('search')
        if search:
            locations = locations.filter(
                Q(name__icontains=search) | 
                Q(address__icontains=search)
            )
            
        # Filter by owner
        owner_id = request.query_params.get('owner_id')
        if owner_id:
            locations = locations.filter(owned_by_id=owner_id)
            
        # Check if we have any results before expensive operations
        if not locations.exists():
            return Response({"locations": [], "count": 0}, status=status.HTTP_200_OK)
        
        # Only select_related for fields we actually need
        locations = locations.select_related('created_by')
        
        # Execute the query and get location IDs for bulk fetching
        locations_list = list(locations)
        location_ids = [loc.id for loc in locations_list]
        
        # Use raw SQL to fetch all ownership shares in a single query
        from django.db import connection
        
        ownership_map = {}
        contact_map = {}
        location_types_map = {}
        
        with connection.cursor() as cursor:
            # Fetch all ownership shares with contact data in one query
            cursor.execute("""
                SELECT 
                    ps.location_id,
                    ps.id as share_id,
                    ps.percentage,
                    ps.is_primary_owner,
                    ps.our_company,
                    c.id as contact_id,
                    c.name as contact_name,
                    c.email as contact_email,
                    c.phone as contact_phone
                FROM 
                    locations_partnershare ps
                INNER JOIN 
                    contacts_contact c ON ps.contact_id = c.id
                WHERE 
                    ps.location_id = ANY(%s)
                ORDER BY 
                    ps.location_id, ps.percentage DESC
            """, [location_ids])
            
            for row in cursor.fetchall():
                location_id, share_id, percentage, is_primary, our_company, contact_id, contact_name, contact_email, contact_phone = row
                location_id_str = str(location_id)
                
                if location_id_str not in ownership_map:
                    ownership_map[location_id_str] = []
                
                # Create a mock share object with contact data
                share_data = {
                    'id': share_id,
                    'percentage': float(percentage),
                    'is_primary_owner': is_primary,
                    'our_company': our_company,
                    'contact': {
                        'id': contact_id,
                        'name': contact_name,
                        'email': contact_email,
                        'phone': contact_phone
                    }
                }
                ownership_map[location_id_str].append(share_data)
        
        with connection.cursor() as cursor:
            # Fetch location types in one query
            cursor.execute("""
                SELECT 
                    ll.location_id,
                    lt.id as type_id,
                    lt.name as type_name
                FROM 
                    locations_location_types ll
                INNER JOIN 
                    locations_locationtype lt ON ll.locationtype_id = lt.id
                WHERE 
                    ll.location_id = ANY(%s)
                ORDER BY 
                    lt.name
            """, [location_ids])
            
            for row in cursor.fetchall():
                location_id, type_id, type_name = row
                location_id_str = str(location_id)
                
                if location_id_str not in location_types_map:
                    location_types_map[location_id_str] = []
                
                location_types_map[location_id_str].append({
                    'id': type_id,
                    'name': type_name
                })
        
        # Fetch reservation data using the existing raw SQL approach
        reservation_map = {}
        
        with connection.cursor() as cursor:
            cursor.execute(""" 
                SELECT 
                    r.location_id, 
                    r.id, 
                    r.start_date, 
                    r.end_date,
                    r.status,
                    r.is_deleted,
                    r.required_capacity,
                    c.id as contact_id,
                    c.name as contact_name
                FROM 
                    reservations_reservation r
                INNER JOIN 
                    contacts_contact c ON r.contact_id = c.id
                WHERE 
                    r.location_id = ANY(%s) AND
                    r.is_deleted = FALSE
                ORDER BY 
                    r.start_date DESC
            """, [location_ids])
            
            for row in cursor.fetchall():
                location_id, res_id, start_date, end_date, res_status, is_deleted, required_capacity, contact_id, contact_name = row
                location_id_str = str(location_id)
                
                if location_id_str not in reservation_map:
                    reservation_map[location_id_str] = []
                
                reservation_map[location_id_str].append({
                    'id': res_id,
                    'start_date': start_date,
                    'end_date': end_date,
                    'status': res_status,
                    'required_capacity': required_capacity,
                    'contact': {
                        'id': contact_id,
                        'name': contact_name
                    }
                })
        
        # Attach all the prefetched data to locations
        for location in locations_list:
            location_id_str = str(location.id)
            
            # Attach ownership data
            location._ownership_shares_data = ownership_map.get(location_id_str, [])
            
            # Attach location types data
            location._location_types_data = location_types_map.get(location_id_str, [])
            
            # Attach reservation data
            location.reservations_list = reservation_map.get(location_id_str, [])
        
        # Use the optimized serializer
        serializer = LocationSimpleSerializer(locations_list, many=True)
        
        return Response({
            "locations": serializer.data,
            "count": len(locations_list)
        }, status=status.HTTP_200_OK)
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve locations", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_location(request, location_id):
    try:
        # Use select_related and prefetch_related for optimized query
        location = Location.objects.select_related(
            'created_by', 
            'updated_by'
        ).prefetch_related(
            'types',
            Prefetch(
                'ownership_shares',
                queryset=PartnerShare.objects.select_related('contact'),
                to_attr='ownership_shares_list'
            ),
            Prefetch(
                'reservations',
                queryset=Reservation.objects.filter(is_deleted=False).order_by('-start_date').select_related('contact'),
                to_attr='reservations_list'
            ),
            'history__modified_by'
        ).get(id=location_id)
        
        serializer = LocationSimpleSerializer(location)
        return Response(serializer.data)
    except Location.DoesNotExist:
        return Response(
            {"error": f"Location with ID {location_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve location", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_location(request, location_id):
    try:
        with transaction.atomic():
            # Prefetch all related data to avoid N+1 queries
            location = get_object_or_404(
                Location.objects.select_related('created_by', 'updated_by').prefetch_related(
                    Prefetch(
                        'ownership_shares',
                        queryset=PartnerShare.objects.select_related('contact'),
                        to_attr='_prefetched_ownership_shares'
                    ),
                    'types'
                ),
                id=location_id
            )
            
            # Capture the previous state before making changes
            previous_data = location.to_dict()
            
            # Use the specialized serializer for ownership updates
            from locations.serializers.locations import LocationOwnershipUpdateSerializer
            serializer = LocationOwnershipUpdateSerializer(
                location,
                data=request.data,
                partial=True
            )
            
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid data", "details": serializer.errors}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Check if name is being changed to an existing one
            if 'name' in serializer.validated_data:
                new_name = serializer.validated_data['name']
                if (new_name != location.name and 
                    Location.objects.filter(name=new_name, is_deleted=False).exists()):
                    return Response(
                        {"error": "Another location with this name already exists"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Update with current user as updater
            serializer.validated_data['updated_by'] = request.user
            location = serializer.save()
            
            # Create history record with the previous state
            location.create_history_record(previous_data, request.user)
            
            # Delete old notifications for the location before creating new ones
            delete_notifications_by_category("location", str(location.id))
            
            # Create notification for location update
            from notifications.utils import create_system_notification
            
            # Determine what was changed
            changes = []
            current_data = location.to_dict()
            
            # Check key fields for changes
            if previous_data.get('name') != current_data.get('name'):
                changes.append(f"name changed from '{previous_data.get('name')}' to '{current_data.get('name')}'")
            
            if previous_data.get('address') != current_data.get('address'):
                changes.append("address changed")
            
            if previous_data.get('capacity') != current_data.get('capacity'):
                changes.append(f"capacity changed from {previous_data.get('capacity')} to {current_data.get('capacity')}")
                
            if previous_data.get('is_active') != current_data.get('is_active'):
                changes.append(f"active status changed to {'active' if current_data.get('is_active') else 'inactive'}")
                
            if previous_data.get('is_reserved') != current_data.get('is_reserved'):
                changes.append(f"reservation status changed to {'reserved' if current_data.get('is_reserved') else 'not reserved'}")
                
            # Check if types changed
            prev_types = {t['name'] for t in previous_data.get('types', [])}
            curr_types = {t['name'] for t in current_data.get('types', [])}
            
            if prev_types != curr_types:
                changes.append("location types changed")
            
            # Check if ownership shares changed
            prev_shares = {(s['contact_id'], float(s['percentage'])) for s in previous_data.get('ownership_shares', [])}
            curr_shares = {(s['contact_id'], float(s['percentage'])) for s in current_data.get('ownership_shares', [])}
            
            if prev_shares != curr_shares:
                changes.append("ownership shares updated")
            
            change_description = ", ".join(changes) if changes else "details updated"
            
            create_system_notification(
                title=f"Location '{location.name}' Updated",
                title_ar=f"تم تحديث الموقع '{location.name}'",
                message=f"The location '{location.name}' was updated by {request.user.email}: {change_description}",
                message_ar=f"تم تحديث الموقع '{location.name}' بواسطة {request.user.email}: {change_description}",
                priority="low",
                notification_type_name="Location Update",
                category="location",
                category_id=str(location.id) if location.id else None
            )
            
            # Fetch the updated location with all related data for response
            returnLocation = Location.objects.select_related(
                'created_by', 'updated_by'
            ).prefetch_related(
                'types',
                Prefetch(
                    'ownership_shares', 
                    queryset=PartnerShare.objects.select_related('contact'),
                    to_attr='ownership_shares_list'
                ),
                Prefetch(
                    'reservations',
                    queryset=Reservation.objects.filter(
                        is_deleted=False
                    ).order_by('-start_date').select_related('contact'),
                    to_attr='reservations_list'
                )
            ).get(id=location.id)
            
            detail_serializer = LocationSimpleSerializer(returnLocation, many=False)
            return Response(detail_serializer.data)
    except Location.DoesNotExist:
        return Response(
            {"error": f"Location with ID {location_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return Response(
            {"error": "Failed to update location", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_location_history(request, location_id):
    """Retrieve history records for a specific location"""
    try:
        # First check if location exists
        location = get_object_or_404(Location, id=location_id)
        
        # Get all history records for this location with select_related for optimization
        history_records = LocationHistory.objects.filter(location=location).select_related('modified_by').order_by('-modified_at')
        
        # Handle case of no history records
        if not history_records.exists():
            return Response({
                "location_id": location_id,
                "location_name": location.name,
                "history": [],
                "message": "No history records found for this location"
            })
        
        # Serialize the history records
        serializer = LocationHistorySerializer(history_records, many=True)
        
        return Response({
            "location_id": location_id,
            "location_name": location.name,
            "history": serializer.data,
            "count": history_records.count()
        })
    except Location.DoesNotExist:
        return Response(
            {"error": f"Location with ID {location_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve location history", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_partner_share(request, location_id, partner_id):
    """Update percentage for a specific partner in a location's ownership shares"""
    try:
        with transaction.atomic():
            location = get_object_or_404(Location, id=location_id)
            
            # Get the specific partner share
            try:
                partner_share = PartnerShare.objects.get(location=location, contact_id=partner_id)
            except PartnerShare.DoesNotExist:
                return Response(
                    {"error": f"Partner with ID {partner_id} is not an owner of this location"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Capture the previous state before making changes
            previous_data = location.to_dict()
            
            # Get the new percentage from request data
            try:
                percentage = float(request.data.get('percentage', 0))
                if percentage <= 0 or percentage > 100:
                    return Response(
                        {"error": "Percentage must be between 0 and 100"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except (TypeError, ValueError):
                return Response(
                    {"error": "Invalid percentage value"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update just the percentage
            partner_share.percentage = percentage
            partner_share.save()
            
            # Update location's updated_by and updated_at
            location.updated_by = request.user
            location.save(update_fields=['updated_by'])
            
            # Create history record with the previous state
            location.create_history_record(previous_data, request.user)
            
            return Response({
                "id": str(partner_share.id),
                "contact_id": str(partner_share.contact_id),
                "contact_name": partner_share.contact.name,
                "location_id": str(location.id),
                "location_name": location.name,
                "percentage": float(partner_share.percentage)
            })
    except Exception as e:
        return Response(
            {"error": "Failed to update ownership share", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_location_types(request):
    """List all location types"""
    try:
        location_types = LocationType.objects.all()
        serializer = LocationTypeSerializer(location_types, many=True)
        return Response({"location_types": serializer.data, "count": location_types.count()})
    except Exception as e:
        return Response(
            {"error": "Failed to retrieve location types", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_location_type(request):
    """Create a new location type"""
    try:
        serializer = LocationTypeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if type with same name already exists
        name = serializer.validated_data['name']
        if LocationType.objects.filter(name__iexact=name).exists():
            return Response(
                {"error": f"Location type '{name}' already exists"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Create new location type
        location_type = serializer.save(created_by=request.user)
        
        return Response(
            LocationTypeSerializer(location_type).data,
            status=status.HTTP_201_CREATED
        )
    except Exception as e:
        return Response(
            {"error": "Failed to create location type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_location_type(request, type_id):
    """Delete a location type"""
    try:
        location_type = get_object_or_404(LocationType, id=type_id)
        
        # Check if type is used by any locations
        if location_type.locations.exists():
            return Response(
                {"error": f"Cannot delete location type '{location_type.name}' as it is being used by locations"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Store type name for response
        type_name = location_type.name
        
        # Delete the type
        location_type.delete()
        
        return Response({
            "message": f"Location type '{type_name}' has been deleted"
        }, status=status.HTTP_200_OK)
    except LocationType.DoesNotExist:
        return Response(
            {"error": f"Location type with ID {type_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": "Failed to delete location type", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def get_location_finances(request):
    """
    Retrieve all income and expense events associated with a specific location.
    
    Expects a POST request with location_id in the body.
    Returns a structured response with income and expense data.
    """
    try:
        # Get location_id from request body
        location_id = request.data.get('location_id')
        if not location_id:
            return Response(
                {"error": "location_id is required"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Verify the location exists
        try:
            location = Location.objects.get(id=location_id)
        except Location.DoesNotExist:
            return Response(
                {"error": f"Location with ID {location_id} not found"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get income data using the internal function
        from income.api.income import get_income_by_location_internal
        income_data = get_income_by_location_internal(location_id)
        
        # Get expense data using the internal function
        from expenses.api.expenses import get_expense_by_loaction_internal
        expense_data = get_expense_by_loaction_internal(location_id)
        
        # Calculate net amount
        total_income = income_data.get('total', 0)
        total_expenses = expense_data.get('total', 0)
        net_amount = total_income - total_expenses
        
        # Return structured response
        return Response({
            "location": {
                "id": str(location.id),
                "name": location.name,
                "address": location.address,
                "capacity": location.capacity
            },
            "finances": {
                "income": {
                    "events": income_data.get('incomes', []),
                    "count": income_data.get('count', 0),
                    "total": total_income
                },
                "expenses": {
                    "events": expense_data.get('expenses', []),
                    "count": expense_data.get('count', 0),
                    "total": total_expenses
                },
                "net_amount": float(net_amount)
            }
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to retrieve financial data for location", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def soft_delete_location(request):
    """
    Soft delete a location.
    
    Checks before deletion:
    - If active reservations exist (end_date in the future), returns an error
    - If income/expense events with status other than 'completed' exist, returns an error
    - If active contracts exist (end_date in the future), marks them as deleted
    
    Takes location_id in the request body.
    Optimized to avoid N+1 query problems.
    """
    try:
        # Get location_id from request body
        location_id = request.data.get('location_id')
        if not location_id:
            return Response(
                {"error": "location_id is required in the request body"}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        with transaction.atomic():
            # Get the location with all needed related data in one query
            location = get_object_or_404(
                Location.objects.select_related('created_by', 'updated_by'),
                id=location_id
            )
            
            # Capture previous state for history
            previous_data = location.to_dict()
            
            # Get current time for comparison
            current_time = timezone.now()
            
            # Check if the location has active reservations
            from reservations.models.reservations import Reservation
            active_reservations = list(Reservation.objects.select_related(
                'contact'
            ).filter(
                location=location,
                is_deleted=False,
                end_date__gt=current_time,
                status__in=['pending', 'active']
            ))
            
            if active_reservations:
                # Get the first active reservation for the error message
                active_reservation = active_reservations[0]
                
                # Format the message with reservation details
                return Response(
                    {
                        "error": "Cannot delete this location because it has active reservations",
                        "details": f"Location is booked until {active_reservation.end_date} in reservation '{active_reservation.title}'. Please edit or cancel the reservation first."
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if location has non-completed income events
            from income.models.incomeEvents import Income
            active_income_events = list(Income.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                location=location,
                is_deleted=False
            ).exclude(status='completed').exclude(status='cancelled'))
            
            if active_income_events:
                # Get the first active income for error message
                active_income = active_income_events[0]
                
                # Format message with income details
                return Response(
                    {
                        "error": "Cannot delete this location because it has active income events",
                        "details": f"Location has an active income event '{active_income.title}' with status '{active_income.get_status_display()}' and due date {active_income.due_date}. Please complete or cancel this income event first."
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if location has non-completed expense events
            from expenses.models.expensesEvents import Event
            active_expense_events = list(Event.objects.select_related(
                'contact', 'location', 'type'
            ).filter(
                location=location,
                is_deleted=False
            ).exclude(status='completed').exclude(status='cancelled'))
            
            if active_expense_events:
                # Get the first active expense for error message
                active_expense = active_expense_events[0]
                
                # Format message with expense details
                return Response(
                    {
                        "error": "Cannot delete this location because it has active expense events",
                        "details": f"Location has an active expense event '{active_expense.title}' with status '{active_expense.get_status_display()}' and due date {active_expense.due_date}. Please complete or cancel this expense event first."
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if the location has active contracts
            from contracts.models.contracts import Contract, ContractHistory
            active_contracts = list(Contract.objects.select_related(
                'contact', 'location'
            ).prefetch_related(
                'documents', 'expense_events', 'income_events'
            ).filter(
                location=location,
                is_deleted=False,
                end_date__gt=current_time
            ))
            
            # Track how many contracts were marked as deleted
            deleted_contracts_count = len(active_contracts)
            contract_history_records = []
            
            # Prepare bulk updates for contracts and related income/expense events
            contract_updates = []
            income_updates = []
            income_history_records = []
            expense_updates = []
            expense_history_records = []
            
            if active_contracts:
                # Collect all contract IDs
                contract_ids = [contract.id for contract in active_contracts]
                
                # Process all contracts
                for contract in active_contracts:
                    # Store contract state before deletion
                    contract_previous_data = contract.to_dict()
                    
                    # Mark contract as deleted
                    contract.is_deleted = True
                    contract.updated_by = request.user
                    contract_updates.append(contract)
                    
                    # Create history record
                    contract_history_records.append(
                        ContractHistory(
                            contract=contract,
                            modified_by=request.user,
                            data=contract_previous_data
                        )
                    )
            
            # Save the original name before changing it
            original_name = location.name
            
            # Update location name to indicate it's deleted
            location.name = f"{location.name} [DELETED]"
            location.is_deleted = True
            location.updated_by = request.user
            location.save(update_fields=['name', 'is_deleted', 'updated_by'])
            
            # Create history record for location
            LocationHistory.objects.create(
                location=location,
                modified_by=request.user,
                data=previous_data
            )
            
            # Perform bulk updates for all related entities
            if contract_updates:
                Contract.objects.bulk_update(
                    contract_updates, 
                    ['is_deleted', 'updated_by']
                )
                
            if contract_history_records:
                ContractHistory.objects.bulk_create(contract_history_records)
            
            # Delete old notifications for this location before creating new ones
            delete_notifications_by_category("location", str(location.id))
            
            # Create notification for location deletion
            message = f"The location '{original_name}' was deleted by {request.user.email}"
            if deleted_contracts_count > 0:
                message += f", along with {deleted_contracts_count} associated contracts"
                
            create_system_notification(
                title=f"Location '{original_name}' Deleted",
                title_ar=f"تم حذف الموقع '{original_name}'",
                message=message,
                message_ar=f"تم حذف الموقع '{original_name}' بواسطة {request.user.email}",
                priority="medium",
                notification_type_name="Location Deleted",
                category="location",
                category_id=str(location.id) if location.id else None
            )
            
            # Return success response with details
            response_data = {
                "message": f"Location '{original_name}' has been deleted successfully",
                "details": {
                    "location_id": str(location.id),
                    "location_name": original_name
                }
            }
            
            if deleted_contracts_count > 0:
                response_data["details"]["associated_contracts_deleted"] = deleted_contracts_count
            
            return Response(response_data, status=status.HTTP_200_OK)
            
    except Location.DoesNotExist:
        return Response(
            {"error": f"Location with ID {location_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())  # Print full traceback for debugging
        return Response(
            {"error": "Failed to delete location", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@numOfQueriesWraper
def update_location_reservation_for_not_ours(request, location_id):
    """
    Update location reservation status and dates.
    
    Accepts:
    - is_reserved: boolean
    - res_start_date: datetime (optional)
    - res_end_date: datetime (optional)
    """
    try:
        with transaction.atomic():
            # Get the location
            location = get_object_or_404(Location, id=location_id)
            
            # Capture the previous state before making changes
            previous_data = location.to_dict()
            
            # Get data from request
            is_reserved = request.data.get('is_reserved')
            res_start_date = request.data.get('res_start_date')
            res_end_date = request.data.get('res_end_date')
            
            # Validate is_reserved if provided
            if is_reserved is not None:
                if not isinstance(is_reserved, bool):
                    return Response(
                        {"error": "is_reserved must be a boolean value"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                location.is_reserved = is_reserved
            
            # Parse and validate dates if provided
            if res_start_date is not None:
                try:
                    from django.utils.dateparse import parse_datetime
                    parsed_start_date = parse_datetime(res_start_date)
                    if parsed_start_date is None:
                        return Response(
                            {"error": "Invalid res_start_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    location.res_start_date = parsed_start_date
                except (ValueError, TypeError):
                    return Response(
                        {"error": "Invalid res_start_date format"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            if res_end_date is not None:
                try:
                    from django.utils.dateparse import parse_datetime
                    parsed_end_date = parse_datetime(res_end_date)
                    if parsed_end_date is None:
                        return Response(
                            {"error": "Invalid res_end_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    location.res_end_date = parsed_end_date
                except (ValueError, TypeError):
                    return Response(
                        {"error": "Invalid res_end_date format"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Validate that end date is after start date if both are provided
            if location.res_start_date and location.res_end_date:
                if location.res_end_date <= location.res_start_date:
                    return Response(
                        {"error": "Reservation end date must be after start date"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # If location is being marked as reserved, ensure dates are provided
            if location.is_reserved:
                if not location.res_start_date or not location.res_end_date:
                    return Response(
                        {"error": "Both res_start_date and res_end_date are required when marking location as reserved"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Update location
            location.updated_by = request.user
            location.save()
            
            # Create history record with the previous state
            location.create_history_record(previous_data, request.user)
            
            # Delete old notifications for the location before creating new ones
            delete_notifications_by_category("location", str(location.id))
            
            # Create notification for reservation update
            if location.is_reserved:
                message = f"Location '{location.name}' was marked as reserved from {location.res_start_date.strftime('%Y-%m-%d %H:%M')} to {location.res_end_date.strftime('%Y-%m-%d %H:%M')} by {request.user.email}"
                title = f"Location '{location.name}' Reserved"
                title_ar = f"تم حجز الموقع '{location.name}'"
            else:
                message = f"Location '{location.name}' reservation status was updated by {request.user.email}"
                title = f"Location '{location.name}' Reservation Updated"
                title_ar = f"تم تحديث حجز الموقع '{location.name}'"
            
            create_system_notification(
                title=title,
                title_ar=title_ar,
                message=message,
                message_ar=message,
                priority="low",
                notification_type_name="Location Reservation Update",
                category="location",
                category_id=str(location.id)
            )
            
            # Return updated location data
            return Response({
                "id": str(location.id),
                "name": location.name,
                "is_reserved": location.is_reserved,
                "res_start_date": location.res_start_date.isoformat() if location.res_start_date else None,
                "res_end_date": location.res_end_date.isoformat() if location.res_end_date else None,
                "message": "Location reservation status updated successfully"
            }, status=status.HTTP_200_OK)
            
    except Location.DoesNotExist:
        return Response(
            {"error": f"Location with ID {location_id} not found"}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return Response(
            {"error": "Failed to update location reservation", "details": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )