import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone
from expenses.models.expensesEvents import Event
from income.models.incomeEvents import Income
from notifications.utils import create_system_notification

class Command(BaseCommand):
    help = 'Check for expenses and income items due today and send notifications'

    def handle(self, *args, **kwargs):
        today = timezone.now().date()
        self.stdout.write(f"Checking for items due on {today}...")
        
        # Check expenses due today
        self._check_expenses(today)
        
        # Check income due today
        self._check_income(today)
        
        self.stdout.write(self.style.SUCCESS("Due date notification check completed!"))
    
    def _check_expenses(self, today):
        """Check for expenses due today and send notifications"""
        # Get expenses with due dates today that are not completed or cancelled
        due_expenses = Event.objects.filter(
            due_date__date=today,
            status__in=['pending', 'upcoming', 'overdue'],
            is_deleted=False,
            paid_date__isnull=True
        )
        
        expense_count = due_expenses.count()
        self.stdout.write(f"Found {expense_count} expenses due today")
        
        # Create notifications for each due expense
        for expense in due_expenses:
            notification_title = f"Expense Due Today: {expense.title}"
            notification_title_ar = f"المصروف المستحق اليوم: {expense.title}"
            notification_message = f"The expense '{expense.title}' for {expense.amount} is due today. You need to pay it."
            notification_message_ar = f"المصروف '{expense.title}' بمبلغ {expense.amount} مستحق اليوم. يجب عليك دفعه."

            create_system_notification(
                title=notification_title,
                title_ar=notification_title_ar,
                message=notification_message,
                message_ar=notification_message_ar,
                priority="high",  # Using high priority since it's due today
                notification_type_name="Expense Due"
            )
            
            self.stdout.write(f"Created notification for expense: {expense.title}")
    
    def _check_income(self, today):
        """Check for income due today and send notifications"""
        # Get income with due dates today that are not completed or cancelled
        due_income = Income.objects.filter(
            due_date__date=today,
            status__in=['pending', 'upcoming', 'overdue'],
            is_deleted=False,
            received_date__isnull=True
        )
        
        income_count = due_income.count()
        self.stdout.write(f"Found {income_count} income items due today")
        
        # Create notifications for each due income
        for income in due_income:
            notification_title = f"Income Due Today: {income.title}"
            notification_title_ar = f"الدخل المستحق اليوم: {income.title}"
            notification_message = f"The income '{income.title}' for {income.amount} is due today. You need to get it."
            notification_message_ar = f"الدخل '{income.title}' بمبلغ {income.amount} مستحق اليوم. يجب عليك جمعه."

            create_system_notification(
                title=notification_title,
                title_ar=notification_title_ar,
                message=notification_message,
                message_ar=notification_message_ar,
                priority="high",  # Using high priority since it's due today
                notification_type_name="Income Due"
            )
            
            self.stdout.write(f"Created notification for income: {income.title}")
